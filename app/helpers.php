<?php

use App\Models\Advertisement;
use App\Models\Candidate;
use App\Models\Cms;
use App\Models\Company;
use App\Models\Cookies;
use App\Models\Education;
use App\Models\Experience;
use App\Models\Job;
use App\Models\JobCategory;
use App\Models\JobCategoryTranslation;
use App\Models\JobRole;
use App\Models\JobRoleTranslation;
use App\Models\JobType;
use App\Models\SalaryType;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\ViewErrorBag;
use Intervention\Image\Facades\Image;
use Laravolt\Avatar\Facade as Avatar;
use Modules\Currency\Entities\Currency;
use Modules\Language\Entities\Language;
use Modules\Location\Entities\Country;
use Modules\Seo\Entities\Seo;
use Stevebauman\Location\Facades\Location;
use Stichoza\GoogleTranslate\GoogleTranslate;
use Torann\GeoIP\Facades\GeoIP;

if (! function_exists('uploadImage')) {
    function uploadImage($file, $destinationPath, $fit = null, $quality = 60)
    {
        $image = Image::make($file);

        $fileName = time().'_'.uniqid().'.'.$file->getClientOriginalExtension();
        $fullPath = public_path($destinationPath.'/'.$fileName);

        if (! File::isDirectory($destinationPath)) {
            File::makeDirectory($destinationPath, 0777, true, true);
        }

        if ($fit) {
            $image->fit($fit[0], $fit[1]);
        }

        $image->save($fullPath, $quality);

        // Return the relative path from the public directory
        return $destinationPath.'/'.$fileName;
    }
}

/**
 * image delete
 *
 * @param  string  $image
 * @return void
 */
if (! function_exists('deleteFile')) {
    function deleteFile(?string $image)
    {
        $imageExists = file_exists($image);

        if ($imageExists) {
            if ($imageExists != 'backend/image/default.png') {
                @unlink($image);
            }
        }
    }
}

/**
 * image delete
 *
 * @param  string  $image
 * @return void
 */
if (! function_exists('deleteImage')) {
    function deleteImage(?string $image)
    {
        $imageExists = file_exists($image);

        if ($imageExists) {
            if ($imageExists != 'backend/image/default.png') {
                @unlink($image);
            }
        }
    }
}

/**
 * @param  UploadedFile  $file
 * @param  null  $folder
 * @param  string  $disk
 * @param  null  $filename
 * @return false|string
 */
if (! function_exists('uploadOne')) {
    function uploadOne(UploadedFile $file, $folder = null, $disk = 'public', $filename = null)
    {
        $name = ! is_null($filename) ? $filename : uniqid('FILE_').dechex(time());

        return $file->storeAs(
            $folder,
            $name.'.'.$file->getClientOriginalExtension(),
            $disk
        );
    }
}

/**
 * @param  null  $path
 * @param  string  $disk
 */
if (! function_exists('deleteOne')) {
    function deleteOne($path = null, $disk = 'public')
    {
        Storage::disk($disk)->delete($path);
    }
}

if (! function_exists('uploadFileToStorage')) {
    function uploadFileToStorage($file, string $path)
    {
        $file_name = $file->hashName();
        Storage::putFileAs($path, $file, $file_name);

        return $path.'/'.$file_name;
    }
}

if (! function_exists('uploadFileToPublic')) {
    function uploadFileToPublic($file, string $path)
    {
        if ($file && $path) {
            $url = $file->move('uploads/'.$path, $file->hashName());
        } else {
            $url = null;
        }

        return $url;
    }
}

// =====================================================
// ===================Env Function====================
// =====================================================
if (! function_exists('envReplace')) {
    function envReplace($name, $value)
    {
        $path = base_path('.env');
        if (file_exists($path)) {
            file_put_contents($path, str_replace(
                $name.'='.env($name),
                $name.'='.$value,
                file_get_contents($path)
            ));
        }

        if (file_exists(App::getCachedConfigPath())) {
            Artisan::call('config:cache');
        }
    }
}
if (! function_exists('replaceAppName')) {
    function replaceAppName($name, $value)
    {
        $path = base_path('.env');
        if (file_exists($path)) {
            // Wrap the value in double quotes and replace the line
            $escapedValue = '"'.str_replace('"', '\"', $value).'"';
            file_put_contents($path, preg_replace(
                "/^$name=.*/m",
                "$name=$escapedValue",
                file_get_contents($path)
            ));
        }

        if (file_exists(App::getCachedConfigPath())) {
            Artisan::call('config:clear');
        }
    }
}

if (! function_exists('envUpdate')) {
    function envUpdate($key, $value)
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        $newLine = "$key=$value";

        if (strpos($envContent, "$key=") !== false) {
            $envContent = preg_replace("/$key=.*/", $newLine, $envContent);
        } else {
            $envContent .= "\n".$newLine;
        }

        file_put_contents($envFile, $envContent);

        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }
}

if (! function_exists('error')) {
    function error($name, $class = 'is-invalid')
    {
        $errors = session()->get('errors', app(ViewErrorBag::class));

        return $errors->has($name) ? $class : '';
    }
}

if (! function_exists('checkSetConfig')) {
    function checkSetConfig($key, $value)
    {

        if ((config($key) != $value)) {

            setConfig($key, $value);
        }
    }
}

if (! function_exists('setConfig')) {
    function setConfig($key, $value)
    {
        Config::write($key, $value);
        sleep(2);
        if (file_exists(App::getCachedConfigPath())) {
            Artisan::call('config:cache');
        }

        return 'Configuration set successfully!';
    }
}

if (! function_exists('allowLaguageChanage')) {
    function allowLaguageChanage()
    {
        return Setting::first()->language_changing ? true : false;
    }
}

// ========================================================
// ===================Response Function====================
// ========================================================

/**
 * Response success data collection
 *
 * @param  object  $data
 * @param  string  $responseName
 * @return \Illuminate\Http\Response
 */
if (! function_exists('responseData')) {
    function responseData(?object $data, string $responseName = 'data')
    {
        return response()->json([
            'success' => true,
            $responseName => $data,
        ], 200);
    }
}

/**
 * Response success data collection
 *
 * @param  string  $msg
 * @return \Illuminate\Http\Response
 */
if (! function_exists('responseSuccess')) {
    function responseSuccess(string $msg = 'Success')
    {
        return response()->json([
            'success' => true,
            'message' => $msg,
        ], 200);
    }
}

/**
 * Response error data collection
 *
 * @param  string  $msg
 * @param  int  $code
 * @return \Illuminate\Http\Response
 */
if (! function_exists('responseError')) {
    function responseError(string $msg = 'Something went wrong, please try again', int $code = 404)
    {
        return response()->json([
            'success' => false,
            'message' => $msg,
        ], $code);
    }
}

/**
 * Response success flash message.
 *
 * @param  string  $msg
 * @return \Illuminate\Http\Response
 */
if (! function_exists('flashSuccess')) {
    function flashSuccess(string $msg)
    {
        session()->flash('success', $msg);
    }
}

/**
 * Response error flash message.
 *
 * @param  string  $msg
 * @return \Illuminate\Http\Response
 */
if (! function_exists('flashError')) {
    function flashError(?string $message = null)
    {
        if (! $message) {
            $message = __('something_went_wrong');
        }

        return session()->flash('error', $message);
    }
}

/**
 * Response warning flash message.
 *
 * @param  string  $msg
 * @return \Illuminate\Http\Response
 */
if (! function_exists('flashWarning')) {
    function flashWarning(?string $message = null, bool $custom = false)
    {
        if (! $message) {
            $message = __('something_went_wrong');
        }

        if ($custom) {
            return session()->flash('warning', $message);
        } else {
            return session()->flash('warning', $message);
        }
    }
}

// ========================================================
// ===================Others Function====================
// ========================================================
if (! function_exists('setting')) {
    function setting($fields = null, $append = false)
    {
        if ($fields) {
            $type = gettype($fields);

            if ($type == 'string') {
                $data = $append ? Setting::first($fields) : Setting::value($fields);
            } elseif ($type == 'array') {
                $data = Setting::first($fields);
            }
        } else {
            $data = loadSetting();
        }

        if ($append) {
            $data = $data->makeHidden(['dark_logo_url', 'light_logo_url', 'favicon_image_url', 'app_pwa_icon_url']);
        }
        forgetCache('setting_data');

        return $data;
    }
}

// For pwa_enable start
if (! function_exists('updateManifest')) {
    function updateManifest($setting)
    {
        $manifest = [

            'name' => config('app.name'),
            'short_name' => config('app.name'),
            'start_url' => route('website.home'),
            'background_color' => $setting->frontend_primary_color,
            'description' => config('app.name'),
            'display' => 'fullscreen',
            'theme_color' => $setting->frontend_primary_color,
            'icons' => [
                [
                    'src' => $setting->app_pwa_icon_url,
                    'sizes' => '512x512',
                    'type' => 'image/png',
                    'purpose' => 'any maskable',
                ],
            ],
        ];

        file_put_contents(public_path('manifest.json'), json_encode($manifest));
    }
}
// For pwa_enable end

if (! function_exists('autoTransLation')) {
    function autoTransLation($lang, $text)
    {
        $tr = new GoogleTranslate($lang);
        $afterTrans = $tr->translate($text);

        return $afterTrans;
    }
}

/**
 * user permission check
 *
 * @param  string  $permission
 * @return bool
 */
if (! function_exists('userCan')) {
    function userCan($permission)
    {
        return auth('admin')->user()->can($permission);
    }
}

if (! function_exists('pdfUpload')) {
    function pdfUpload(?object $file, string $path): string
    {
        $filename = time().'.'.$file->extension();
        $filePath = public_path('uploads/'.$path);
        $file->move($filePath, $filename);

        return $filePath.$filename;
    }
}

if (! function_exists('remainingDays')) {

    function remainingDays($deadline)
    {
        $now = Carbon::now();
        $cDate = Carbon::parse($deadline);

        return $now->diffInDays($cDate);
    }
}

if (! function_exists('jobStatus')) {
    function jobStatus($deadline)
    {
        $now = Carbon::now();
        $cDate = Carbon::parse($deadline);

        if ($now->greaterThanOrEqualTo($cDate)) {
            return 'Expire';
        } else {
            return 'Active';
        }
    }
}

if (! function_exists('socialMediaShareLinks')) {

    function socialMediaShareLinks(string $path, string $provider)
    {
        switch ($provider) {
            case 'facebook':
                $share_link = 'https://www.facebook.com/sharer/sharer.php?u='.$path;
                break;
            case 'twitter':
                $share_link = 'https://twitter.com/intent/tweet?text='.$path;
                break;
            case 'pinterest':
                $share_link = 'http://pinterest.com/pin/create/button/?url='.$path;
                break;
            case 'linkedin':
                $share_link = 'https://www.linkedin.com/shareArticle?mini=true&url='.$path;
                break;
            case 'telegram':
                $share_link = 'https://t.me/share/url?url='.$path;
                break;
            case 'whatsapp':
                $share_link = 'https://api.whatsapp.com/send?text='.$path;
                break;
            case 'linkedin':
                $share_link = 'https://www.linkedin.com/sharing/share-offsite/?url='.$path;
                break;
            case 'mail':
                $share_link = 'mailto:?subject=Share this link&body='.$path;
                break;
            case 'skype':
                $share_link = 'https://web.skype.com/share?url='.$path;
                break;
        }

        return $share_link;
    }
}

if (! function_exists('livejob')) {

    function livejob()
    {
        $jobs = Job::withoutEdited()->openPosition();

        $selected_country = session()->get('selected_country');

        if ($selected_country && $selected_country != null && $selected_country != 'all') {
            $country = selected_country()->name;
            $jobs->where('country', 'LIKE', "%$country%");
        } else {

            $setting = loadSetting();
            if ($setting->app_country_type == 'single_base') {
                if ($setting->app_country) {

                    $country = Country::where('id', $setting->app_country)->first();
                    if ($country) {
                        $jobs->where('country', 'LIKE', "%$country->name%");
                    }
                }
            }
        }

        return $jobs->count();
    }
}

if (! function_exists('companies')) {

    function companies()
    {
        $companies = Company::count();

        return $companies;
    }
}

if (! function_exists('newjob')) {

    function newjob()
    {
        $newjobs = Job::where('status', 'active')->where('created_at', '>=', Carbon::now()->subDays(7)->toDateString())->count();

        return $newjobs;
    }
}

if (! function_exists('candidate')) {
    function candidate()
    {
        $candidates = Candidate::count();

        return $candidates;
    }
}

if (! function_exists('linkActive')) {
    function linkActive($route, $class = 'active')
    {
        return request()->routeIs($route) ? $class : '';
    }
}

if (! function_exists('candidateNotifications')) {
    function candidateNotifications()
    {
        return auth()->user()->notifications()->take(6)->get();
    }
}

if (! function_exists('candidateNotificationsCount')) {

    function candidateNotificationsCount()
    {

        return auth()->user()->notifications()->count();
    }
}

if (! function_exists('candidateUnreadNotifications')) {

    function candidateUnreadNotifications()
    {
        return auth()->user()->unreadNotifications()->count();
    }
}

if (! function_exists('companyNotifications')) {

    function companyNotifications()
    {

        return auth()->user()->notifications()->take(6)->get();
    }
}

if (! function_exists('companyNotificationsCount')) {
    function companyNotificationsCount()
    {
        return auth()->user()->notifications()->count();
    }
}

if (! function_exists('companyUnreadNotifications')) {

    function companyUnreadNotifications()
    {

        return auth()->user()->unreadNotifications()->count();
    }
}

if (! function_exists('defaultCurrencySymbol')) {
    function defaultCurrencySymbol()
    {
        return config('templatecookie.app_currency_symbol');
    }
}

if (! function_exists('currencyAmountShort')) {

    function currencyAmountShort($amount)
    {
        $num = $amount * getCurrencyRate();
        $units = ['', 'K', 'M', 'B', 'T'];
        for ($i = 0; $num >= 1000; $i++) {
            $num /= 1000;
        }

        return round($num, 0).$units[$i];
    }
}

/* Currency position
 *
 * @param String $date
 */
if (! function_exists('changeCurrency')) {
    function changeCurrency($amount)
    {
        if (session()->has('current_currency')) {
            $current_currency = session('current_currency');
            $symbol = $current_currency->symbol;
            $position = $current_currency->symbol_position;
        } else {
            $symbol = config('templatecookie.currency_symbol');
            $position = config('templatecookie.currency_symbol_position');
        }

        $converted_amount = round($amount * getCurrencyRate(), 2);

        if ($position == 'left') {
            return $symbol.' '.$converted_amount;
        } else {
            return $converted_amount.' '.$symbol;
        }
    }
}

/**
 * Remove the decimal numbers and shorten
 *
 * @param  number  $amount
 * @return string
 */
if (! function_exists('zeroDecimal')) {
    function zeroDecimal($amount)
    {
        $units = ['', 'K', 'M', 'B', 'T'];
        for ($i = 0; $amount >= 1000; $i++) {
            $amount /= 1000;
        }

        return round($amount, 0).$units[$i];
    }
}

/**
 * Currency exchange
 *
 * @param  $amount
 * @param  $from
 * @param  $to
 * @param  $round
 * @return number
 */
if (! function_exists('currencyExchange')) {
    function currencyExchange($amount, $from = null, $to = null, $round = 2)
    {
        $from = currentCurrencyCode();
        $to = config('templatecookie.currency', 'USD');

        $fromRate = Currency::whereCode($from)->first()->rate;
        $toRate = Currency::whereCode($to)->first()->rate;
        $rate = $toRate / $fromRate;

        return round($amount * $rate);
    }
}

// if (! function_exists('currencyConversion')) {
//     function currencyConversion($amount, $to = 'USD', $round = 2)
//     {
//         $to = $to;

//         $checkCurrency = Currency::where('code', $to)->first();

//         if ($amount && $checkCurrency) {
//             $total = $amount * $checkCurrency->rate;

//             return (int) round($total, $round);
//         }

//         return $amount;

//         // $from = $from ?? config('templatecookie.currency');
//         // $to = $to ?? 'USD';

//         // $checkCurrency = Currency::where('code', $to)->first();
//         // if ($amount && $checkCurrency) {

//         //     $fromRate = Currency::whereCode($from)->first()?->rate ?? 1;
//         //     $toRate = Currency::whereCode($to)->first()?->rate ?? 1;
//         //     $rate = $fromRate / $toRate;
//         //     $result = $amount / $rate;

//         //     return (int) round($amount * $rate, 2);
//         // }

//         // $from = $from ?? config('templatecookie.currency');
//         // $to = $to ?? 'USD';

//         // $checkCurrency = Currency::where('code', $to)->first();
//         // if ($amount && $checkCurrency) {

//         //     $fromRate = Currency::whereCode($from)->first()?->rate ?? 1;
//         //     $toRate = Currency::whereCode($to)->first()?->rate ?? 1;
//         //     $rate = $fromRate / $toRate;
//         //     $result = $amount / $rate;

//         //     return (int) round($amount * $rate, 2);
//         // }
//     }
// }

if (! function_exists('currencyConversion')) {
    function currencyConversion($amount, $to = 'USD', $round = 2)
    {
        $to = $to;

        // Check if the target currency is TL
        if ($to === 'TL') {
            // Assuming 1 USD = 9 TL, replace this value with the actual conversion rate
            $usdToTLRate = 32.27; // For example purposes, replace with actual rate
            $total = $amount * $usdToTLRate;
        } else {
            // Convert to USD or any other currency
            $checkCurrency = Currency::where('code', $to)->first();

            if ($amount && $checkCurrency) {
                $total = $amount * $checkCurrency->rate;
            } else {
                return $amount;
            }
        }

        return (int) round($total, $round);
    }
}

if (! function_exists('usdAmount')) {
    function usdAmount($amount)
    {
        if (session('currency_rate')) {
            return round($amount / session('currency_rate.rate'), 2);
        } else {
            return round($amount * 1 / Currency::whereCode(config('templatecookie.currency'))->first()->rate, 2);
        }
    }
}

if (! function_exists('currencyConvert')) {
    function currencyConvert($amount, $to = 'USD', $round = 2)
    {
        $checkCurrency = Currency::where('code', $to)->first();

        if ($amount && $checkCurrency) {
            $total = $amount * $checkCurrency->rate;

            return round($total, $round);
        }

        return $amount;
    }
}

/**
 * Currency rate store in session
 *
 * @return void
 */
if (! function_exists('currencyRateStore')) {
    function currencyRateStore()
    {
        if (session()->has('currency_rate')) {
            $currency_rate = session('currency_rate');
            $from = config('templatecookie.currency');
            $to = currentCurrencyCode();

            if ($currency_rate['from'] != $from || $currency_rate['to'] != $to) {
                $fromRate = Currency::whereCode($from)->first()->rate;
                $toRate = Currency::whereCode($to)->first()->rate;
                $rate = $fromRate / $toRate;
                session(['currency_rate' => ['from' => $from, 'to' => $to, 'rate' => $rate]]);
            }
        } else {
            $from = config('templatecookie.currency');
            $to = currentCurrencyCode();

            $fromRate = Currency::whereCode($from)->first()->rate;
            $toRate = Currency::whereCode($to)->first()->rate;
            $rate = $fromRate / $toRate;
            session(['currency_rate' => ['from' => $from, 'to' => $to, 'rate' => $rate]]);
        }
    }
}

/**
 * Get currency rate
 *
 * @return number
 */
if (! function_exists('getCurrencyRate')) {
    function getCurrencyRate()
    {
        if (session()->has('currency_rate')) {
            $currency_rate = session('currency_rate');
            $rate = $currency_rate['rate'];

            return $rate;
        } else {
            return 1;
        }
    }
}

/**
 * Get current Currency
 *
 * @return object
 */
if (! function_exists('currentCurrency')) {
    function currentCurrency()
    {
        return session('current_currency') ?? loadSystemCurrency();
    }
}

/**
 * Get current Currency code
 *
 * @return string
 */
if (! function_exists('currentCurrencyCode')) {
    function currentCurrencyCode()
    {
        if (session()->has('current_currency')) {
            $currency = session('current_currency');

            return $currency->code;
        }

        return config('templatecookie.currency');
    }
}

/**
 * Get current Currency symbol
 *
 * @return string
 */
if (! function_exists('currentCurrencySymbol')) {
    function currentCurrencySymbol()
    {
        if (session()->has('current_currency')) {
            $currency = session('current_currency');

            return $currency->symbol;
        }

        return config('templatecookie.currency_symbol');
    }
}

if (! function_exists('currentLanguage')) {

    function currentLanguage()
    {

        session('current_lang','az');
        return session('current_lang');
    }
}

if (! function_exists('langDirection')) {

    function langDirection()
    {
        return currentLanguage()?->direction ?? Language::where('code', config('templatecookie.default_language'))->value('direction');
    }
}

if (! function_exists('metaData')) {

    function metaData($page)
    {
        $current_language = currentLanguage(); // current session language
        $language_code = $current_language ? $current_language->code : 'en'; // language code or default one
        $page = Seo::where('page_slug', $page)->first(); // get page
        $exist_content = $page ? $page->contents()->where('language_code', $language_code)->first() : null; // get page content orderBy page && language
        if ($exist_content) {
            $content = $exist_content;
        } else {
            $content = $page->contents()?->where('language_code', 'en')->first() ?? '';
        }

        return $content; // return response
    }
}

if (! function_exists('storePlanInformation')) {

    function storePlanInformation()
    {
        session()->forget('user_plan');
        session(['user_plan' => isset(auth()->user()->company->userPlan) ? auth()->user()->company->userPlan : []]);
    }
}

if (! function_exists('formatTime')) {

    function formatTime($date, $format = 'F d, Y H:i A')
    {
        return Carbon::parse($date)->format($format);
    }
}

if (! function_exists('inspireMe')) {

    function inspireMe()
    {
        Artisan::call('inspire');

        return Artisan::output();
    }
}

if (! function_exists('getUnsplashImage')) {
    function getUnsplashImage()
    {
        $url = 'https://source.unsplash.com/random/1920x1280/?nature,landscape,mountains';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Must be set to true so that PHP follows any "Location:" header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $a = curl_exec($ch); // $a will contain all headers
    }
}

if (! function_exists('adminNotifications')) {
    function adminNotifications()
    {
        return auth('admin')->user()->notifications()->take(10)->get();
    }
}

if (! function_exists('adminUnNotifications')) {

    function adminUnNotifications()
    {
        return auth('admin')->user()->unreadNotifications()->count();
    }
}

if (! function_exists('checkMailConfig')) {

    function checkMailConfig()
    {
        $status = config('mail.mailers.smtp.transport') && config('mail.mailers.smtp.host') && config('mail.mailers.smtp.port') && config('mail.mailers.smtp.username') && config('mail.mailers.smtp.password') && config('mail.mailers.smtp.encryption') && config('mail.from.address') && config('mail.from.name');

        ! $status ? flashError(__('mail_not_sent_for_the_reason_of_incomplete_mail_configuration')) : '';

        return $status ? 1 : 0;
    }
}

if (! function_exists('openJobs')) {

    function openJobs()
    {
        return Job::where('status', 'active')->where('deadline', '>=', Carbon::now()->toDateString())->count();
    }
}

if (! function_exists('updateMap')) {
    function updateMap($data)
    {
        if (empty(config('templatecookie.map_show'))) {
            session()->put('location', [
                'country' => session('selectedCountryId'),
                'region' => session('selectedStateId'),
                'district' => session('selectedCityId'),
                'lng' => session('selectedCityLong') ?? session('selectedStateLong') ?? session('selectedCountryLong'),
                'lat' => session('selectedCityLat') ?? session('selectedStateLat') ?? session('selectedCountryLat'),
            ]);
        }
        $location = session()->get('location');

        if ($location) {
            $region = '';
            $region = array_key_exists('region', $location) ? $location['region'] : '';
            $country = array_key_exists('country', $location) ? $location['country'] : '';
            if ($region == 'undefined') {
                $address = Str::slug($country);
                $region = '';
            } else {
                $address = Str::slug($region.'-'.$country);
            }

            $data->update([
                'address' => $address,
                'neighborhood' => array_key_exists('neighborhood', $location) ? $location['neighborhood'] : '',
                'locality' => array_key_exists('locality', $location) ? $location['locality'] : '',
                'place' => array_key_exists('place', $location) ? $location['place'] : '',
                'district' => array_key_exists('district', $location) ? $location['district'] : '',
                'postcode' => array_key_exists('postcode', $location) ? $location['postcode'] : '',
                'region' => $region ?? '',
                'country' => array_key_exists('country', $location) ? $location['country'] : '',
                'long' => array_key_exists('lng', $location) ? $location['lng'] : '',
                'lat' => array_key_exists('lat', $location) ? $location['lat'] : '',
                'exact_location' => array_key_exists('exact_location', $location) ? $location['exact_location'] : '',
            ]);
            session()->forget('location');
            session([
                'selectedCountryId' => null,
                'selectedStateId' => null,
                'selectedCityId' => null,
                'selectedCountryLong' => null,
                'selectedCountryLat' => null,
                'selectedStateLong' => null,
                'selectedStateLat' => null,
                'selectedCityLong' => null,
                'selectedCityLat' => null,
            ]);
        }

        return true;
    }
}

if (! function_exists('selected_country')) {
    function selected_country()
    {
        $selected_country = session()->get('selected_country');

        if ($selected_country) {
            $countries = loadAllCountries();
            $country = $countries->where('id', $selected_country)->first() ?? loadCountry();

        } else {
            $country = loadCountry();
        }

        return $country;

        // $selected_country = session()->get('selected_country');
        // $country = Country::find($selected_country) ?? Country::first();

        // return $country;
    }
}

if (! function_exists('get_file_size')) {
    function get_file_size($file)
    {
        if (file_exists($file)) {
            $file_size = File::size($file) / 1024 / 1024;

            return round($file_size, 4).' MB';
        }

        return '0 MB';
    }
}

/**
 * Increases or decreases the brightness of a color by a percentage of the current brightness.
 *
 * @param  string  $hexCode  Supported formats: `#FFF`, `#FFFFFF`, `FFF`, `FFFFFF`
 * @param  float  $adjustPercent  A number between -1 and 1. E.g. 0.3 = 30% lighter; -0.4 = 40% darker.
 * @return string
 *
 * <AUTHOR>
 */
if (! function_exists('adjustBrightness')) {
    function adjustBrightness($hexCode, $adjustPercent)
    {
        $hexCode = ltrim($hexCode, '#');

        if (strlen($hexCode) == 3) {
            $hexCode = $hexCode[0].$hexCode[0].$hexCode[1].$hexCode[1].$hexCode[2].$hexCode[2];
        }

        $hexCode = array_map('hexdec', str_split($hexCode, 2));

        foreach ($hexCode as &$color) {
            $adjustableLimit = $adjustPercent < 0 ? $color : 255 - $color;
            $adjustAmount = ceil($adjustableLimit * $adjustPercent);

            $color = str_pad(dechex($color + $adjustAmount), 2, '0', STR_PAD_LEFT);
        }

        return '#'.implode($hexCode);
    }
}

if (! function_exists('current_country_code')) {
    function current_country_code()
    {
        if (selected_country()) {
            $country_code = selected_country()->sortname;
        } else {
            $setting = loadSetting();

            if ($setting->app_country_type != 'multiple_base') {
                $country_code = Country::find($setting->app_country)->sortname;
            } else {
                return '';
            }
        }

        return $country_code;
    }
}

/**
 * Set ip wise country, currency and language
 *
 * @return void
 */
if (! function_exists('setLocationCurrency')) {
    function setLocationCurrency()
    {
        $ip = request()->ip();
        // $ip = '************'; // Bangladesh
        // $ip = '***************'; // Mauritius
        // $ip = '**************'; // Egypt
        // $ip = '************'; // United States"
        // $ip = '***********'; // Czech Republic
        // $ip = "************"; // Czechia

        if ($ip && $ip != '127.0.0.1') {
            $geo = GeoIP::getLocation($ip);

            // Set the currency
            if (! session()->has('current_currency')) {
                $currency = Modules\Currency\Entities\Currency::where('code', $geo->currency)->first() ?? Modules\Currency\Entities\Currency::where('code', config('templatecookie.currency'))->first();

                if ($currency) {
                    session(['current_currency' => $currency]);
                } else {
                    session(['current_currency' => Modules\Currency\Entities\Currency::first()]);
                }
            }

            // Set the language
            if (! session()->has('current_lang')) {
                $path = base_path('resources/backend/dummy-data/country_currency_language.json');
                $country_language_currency = json_decode(file_get_contents($path), true);
                $key = array_search($geo->iso_code, array_column($country_language_currency, 'code'));
                $country_language_currency = $country_language_currency[$key];
                $lang_code = $country_language_currency['language']['code'];
                $language = Language::where('code', $lang_code)->first();

                if ($language) {
                    session(['current_lang' => $language]);
                } else {
                    session(['current_lang' => Language::where('code', config('templatecookie.default_language'))->first()]);
                }
            }

            // Set the country
            // $selected_country = session('country_code');
            $selected_country = session('selected_country');

            if (! session()->has('selected_country')) {
                // if (!session()->has('country_code')) {
                if ($selected_country != 'all') {
                    if ($ip) {
                        $current_user_data = Location::get($ip);
                    }
                    if ($current_user_data) {
                        $user_country = $current_user_data->countryName;
                        if ($user_country) {
                            $database_country = Country::where('name', $user_country)->where('status', 1)->first();
                            if ($database_country) {
                                // $selected_country = session()->get('country_code');
                                $selected_country = session()->get('selected_country');
                                if (! $selected_country) {
                                    // session()->put('country_code', $database_country->sortname);
                                    session()->put('selected_country', $database_country->id);

                                    return true;
                                }
                            }
                        }
                    }
                }
            } else {
                // $selected_country = session('country_code');
                $selected_country = session('selected_country');
            }
        }
    }
}

/**
 * @param  string  $date
 *                        Date format
 */
if (! function_exists('getLanguageByCode')) {
    function getLanguageByCode($code)
    {
        $languages = loadLanguage();

        return $languages->where('code', $code)->value('name');
    }
}

/**
 * @param  string  $date
 *                        Date format
 */
if (! function_exists('getLanguageByCodeInLookUp')) {
    function getLanguageByCodeInLookUp($code, $languages)
    {
        $language = $languages->where('code', $code)->first();

        return $language ? $language->name : '';
    }
}

/**
 * @param  string  $date
 *                        Date format
 */
if (! function_exists('currentLangCode')) {
    function currentLangCode()
    {

        if (session('current_lang')) {
            return session('current_lang')->code;
        } else {
            return loadSystemLanguageCode();
        }
    }
}

/**
 * @param  string  $date
 *                        Date format
 */
if (! function_exists('dateFormat')) {
    function dateFormat($date, $format = 'F Y')
    {
        return \Carbon\Carbon::createFromFormat($format, $date)->toDateTimeString();
    }
}

/* Currency position
    *
    * @param String $date
    */
if (! function_exists('currencyPosition')) {
    function currencyPosition($amount, $applyCurrencyRate = false, $current_currency = null): string
    {
        if (! $current_currency) {
            $current_currency = currentCurrency();
        }
        $symbol = $current_currency->symbol;
        $position = $current_currency->symbol_position;

        if ($applyCurrencyRate) {
            $amount = $amount / getCurrencyRate();
            $amount = round($amount, 2);
        }

        if ($position == 'left') {
            return $symbol.' '.$amount;
        } else {
            return $amount.' '.$symbol;
        }
    }
}

/**
 * Authenticate candidate
 */
if (! function_exists('currentCandidate')) {
    function currentCandidate()
    {
        return authUser()->candidate;
    }
}

/**
 * Authenticate candidate
 */
if (! function_exists('currentCompany')) {
    function currentCompany()
    {
        return authUser()->company;
    }
}

/**
 * Authenticate user
 */
if (! function_exists('authUser')) {
    function authUser()
    {
        return auth('user')->user();
    }
}

/* Get format number for currency
    *
    * @param String $path
    */
if (! function_exists('getFormattedNumber')) {
    function getFormattedNumber(
        $value,
        $currencyCode = 'USD',
        $locale = 'en_US',
        $style = NumberFormatter::DECIMAL,
        $precision = 0,
        $groupingUsed = true,
    ) {
        if (session()->has('current_lang')) {
            $locale = currentLanguage()->code.'_us' ?? $locale;
        }

        $currencyCode = currentCurrencyCode();
        $formatter = new NumberFormatter($locale, $style);
        $formatter->setAttribute(NumberFormatter::FRACTION_DIGITS, $precision);
        $formatter->setAttribute(NumberFormatter::GROUPING_USED, $groupingUsed);
        if ($style == NumberFormatter::CURRENCY) {
            $formatter->setTextAttribute(NumberFormatter::CURRENCY_CODE, $currencyCode);
        }

        return $formatter->format($value / getCurrencyRate());
    }
}

/**
 * Checks jobs status like highlighted or featured
 *
 * @param  string  $date
 * @return bool
 */
if (! function_exists('isFuture')) {
    function isFuture($date = null): bool
    {

        if ($date) {
            return Carbon::parse($date)->isFuture();
        }

        return false;
    }
}

if (! function_exists('getEmailTemplateFormatFlagsByType')) {
    function getEmailTemplateFormatFlagsByType($type)
    {
        return \App\Http\Controllers\Admin\EmailTemplateController::getFormatterByType($type) ?? [];
    }
}

if (! function_exists('getFormattedTextByType')) {
    function getFormattedTextByType($type, $data = null)
    {
        return \App\Http\Controllers\Admin\EmailTemplateController::getFormattedTextByType($type, $data);
    }
}

/**
 * get formatted mail template
 *
 * @param  string  $type
 * @param  mixed  $data
 * @return string formatted mail content
 */
if (! function_exists('getFormattedMail')) {
    function getFormattedMail($type, $data)
    {
        return \App\Http\Controllers\Admin\EmailTemplatesController::formatMessage($type, $data);
    }
}

/**
 * get list of available format flags
 *
 * @param  string  $type
 * @return array list of available format flags
 */
if (! function_exists('getFormatFlagsByType')) {
    function getFormatFlagsByType($type)
    {
        return \App\Http\Controllers\Admin\EmailTemplatesController::getFormatByType($type)['search'] ?? [];
    }
}

/**
 * Create avatar image
 *
 * @param  string  $name
 * @param  string  $path
 * @return bool
 */

//  if (! function_exists('createAvatar')) {
//     function createAvatar($name = null, $path = 'uploads/images'): string
//     {
//         if (! File::exists($path)) {
//             File::makeDirectory($path, $mode = 0777, true, true);
//         }

//         $name = $name ? $name.'_'.time().'_'.uniqid() : time().'_'.uniqid();
//         Avatar::create($name)->setDimension(250, 250)->save("{$path}/{$name}.png", 100);

//         return "{$path}/{$name}.png";
//     }
// }

if (! function_exists('createAvatar')) {
    function createAvatar($name, $path, $setDimension = null, $quality = 60): string
    {
        $mainAvatar = Avatar::create($name);

        if (! File::exists($path)) {
            File::makeDirectory($path, $mode = 0777, true, true);
        }

        $name = $name ? $name.'_'.time().'_'.uniqid() : time().'_'.uniqid();

        if ($setDimension) {
            $mainAvatar->setDimension($setDimension[0], $setDimension[1]);
        }

        // Avatar::create($name)->setDimension($setDimension[0], $setDimension[1])->save("{$path}/{$name}.jpg", $quality);
        $mainAvatar->save($path.'/'.$name.'jpg', $quality);
        // Avatar::create($name)->setDimension($setDimension[0], $setDimension[1])->save("{$path}/{$name}.jpg", $quality);

        // dd("{$path}/{$name}.jpg", $quality);

        // return "{$path}/{$name}.jpg";
        return $path.'/'.$name.'jpg';
    }
}

if (! function_exists('generateReference')) {
    function generateReference(?string $transactionPrefix = null)
    {
        if ($transactionPrefix) {
            return $transactionPrefix.'_'.uniqid(time());
        }

        return 'flw_'.uniqid(time());
    }
}

/**
 * URL match helper
 *
 * @param  string  $current_url
 * @param  string  $value_url
 * @return bool
 */
if (! function_exists('urlMatch')) {
    function urlMatch(?string $current_url = null, ?string $value_url = null)
    {
        if ($current_url == $value_url) {
            return true;
        }

        return false;
    }
}

if (! function_exists('currentLocation')) {
    function currentLocation()
    {
        $ip = request()->ip();
        // $ip = '************'; // Bangladesh
        // $ip = '***************'; // Mauritius
        // $ip = '*************'; // AUD
        // $ip = '*************'; // SA
        // $ip = '************'; // United States"
        // $ip = '***********'; // Czech Republic
        // $ip = "************"; // Czechia

        $current_user_data = Location::get($ip);

        if ($ip && $current_user_data && $current_user_data->countryName) {
            return $current_user_data->countryName;
        }

        return null;
    }
}

if (! function_exists('loadCms')) {
    function loadCms()
    {
        return Cache::remember('Cms', now()->addDays(2), function () {
            return Cms::first();
        });
    }
}

if (! function_exists('loadCurrency')) {
    function loadCurrency()
    {
        return Cache::remember('Currency', now()->addDays(2), function () {
            return Currency::first();
        });
    }
}

if (! function_exists('loadSetting')) {
    function loadSetting()
    {
        return Cache::remember('setting', now()->addDays(2), function () {
            return Setting::first();
        });
    }
}

if (! function_exists('loadLanguage')) {
    function loadLanguage()
    {
        return Cache::remember('languages', now()->addDays(2), function () {
            return Language::all();
        });
    }
}

if (! function_exists('loadCookies')) {
    function loadCookies()
    {
        return Cache::remember('cookies', now()->addDays(30), function () {
            return Cookies::first();
        });
    }
}

if (! function_exists('loadDefaultLanguage')) {
    function loadDefaultLanguage()
    {
        return Cache::remember('default_language', now()->addDays(30), function () {
            return Language::where('code', config('templatecookie.default_language'))->first();
        });
    }
}

if (! function_exists('loadCountry')) {
    function loadCountry()
    {
        return Cache::remember('country', now()->addDays(30), function () {
            return Country::first();
        });
    }
}

if (! function_exists('loadAllCountries')) {
    function loadAllCountries()
    {
        return Cache::remember('countries', now()->addDays(30), function () {
            return Country::all();
        });
    }
}

if (! function_exists('loadActiveCountries')) {
    function loadActiveCountries()
    {
        return Cache::remember('active_countries', now()->addDays(30), function () {
            return Country::select('id', 'name', 'slug', 'icon')->active()->get();
        });
    }
}

if (! function_exists('loadAllCurrencies')) {
    function loadAllCurrencies()
    {
        return Cache::remember('currencies', now()->addDays(30), function () {
            return Currency::all();
        });
    }
}

/**
 * Get the system currency
 *
 * @return object
 */
if (! function_exists('loadSystemCurrency')) {
    function loadSystemCurrency()
    {
        return Cache::remember('systemCurrency', now()->addDays(30), function () {
            // return Modules\Currency\Entities\Currency::where('code', config('jobpilot.currency'))->first();
            return Modules\Currency\Entities\Currency::where('code', config('templatecookie.currency'))->first();
        });
    }
}

/**
 * Get the system currency
 *
 * @return object
 */
if (! function_exists('loadSystemLanguageCode')) {
    function loadSystemLanguageCode()
    {
        return Cache::remember('systemLanguageCode', now()->addDays(30), function () {
            return Language::where('code', config('templatecookie.default_language'))->value('code');
        });
    }
}

/**
 * Getting cache information
 *
 * @return string
 */
if (! function_exists('getCache')) {
    function getCache($name)
    {
        return Cache::get($name);
    }
}

/**
 * Getting cache information
 *
 * @return string
 */
if (! function_exists('forgetCache')) {
    function forgetCache($name)
    {
        Cache::forget($name);
        loadSetting();

        return true;
    }
}

/**
 * @param  $fullName
 * @return string
 */
if (! function_exists('maskFullName')) {
    function maskFullName($fullName)
    {
        $nameParts = explode(' ', $fullName); // Split the full name into parts
        $maskedName = '';

        foreach ($nameParts as $part) {
            $initial = substr($part, 0, 1); // Get the first letter
            $rest = substr($part, 1); // Get the rest of the letters
            $maskedName .= $initial.str_repeat('*', strlen($rest)).' '; // Replace the rest with asterisks
        }

        return rtrim($maskedName); // Remove trailing space and return the masked name
    }
}

if (! function_exists('advertisementCode')) {
    function advertisementCode($page_slug)
    {

        $ads = loadAdvertisements();
        $code = '';
        $ad = $ads->where('page_slug', $page_slug)->where('status', 1)->first();
        if ($ad) {
            $code = $ad->ad_code;
        }

        return $code;
    }
}

if (! function_exists('advertisement_status')) {
    function advertisement_status($page_slug)
    {
        return Advertisement::where('page_slug', '=', $page_slug)->value('status');
    }
}

if (! function_exists('loadAdvertisements')) {
    function loadAdvertisements()
    {
        return Cache::remember('advertisements', now()->addDays(30), function () {
            return Advertisement::all();
        });
    }
}

//custom function for the grabbing the user ip address

//job search .az
if (! function_exists('getCompanyJobBySlug')) {
    function getCompanyJobBySlug($slug)
    {
        $data = Http::withHeaders([
            'authority' => 'jobsearch.az',
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
            'content-type' => 'application/json',
            // 'cookie' => 'user=%7B%22error%22%3Afalse%2C%22favorites_count%22%3A0%7D; _gcl_au=1.1.*********.1686317179; _ga=GA1.2.*********.1686317180; _gid=GA1.2.1333868943.1688149855; dark_mode=true; lang=az; XSRF-TOKEN=eyJpdiI6InRpWHdFanFHUE9nWWJOMUY0dnlGa0E9PSIsInZhbHVlIjoicFlzWVV6ZVZIdEw0R2dMK0YwQlUraENydWdHdEQxSlR0RFQ1QUVScnhjRnYxWUM4bnQ0alpCZXZPL0R2TDViQ1VROEFKV1RJS0Fja0lEOVhRNVNnRGlXQjBXcElyWjIreE9VSFVHUDN6MmJrMlNac1VwY0s0Ym1idTJkbEc5YWciLCJtYWMiOiI2Nzg4ZTk2ZmExMDI4MWY2ZWQ5M2I0YTZiNzNkMWI4MWJjNTdhOWJlM2U2YzIyZDBhMzc0MWRlOWM3YzRjNGYxIn0%3D; JOB_SEARCH=eyJpdiI6Ik91NW4zcjB5eEpHaUI4NXZWRXU2MXc9PSIsInZhbHVlIjoiTEZ5UGZreCtGQndzemdWVFpXQjRad1FEcHpxQ3NCRno2aTZ4ak82ZTFoOGpJcmlNUjdvdi85T2hOeWdMdDlmbktaSXFXc0ZpbXVjKzlXSnFWaTkrVXAweTNtc29rM3pPUWZUL2tjNS8wMG52VXpZWGVOcEYyem1xM3owTWZCQVgiLCJtYWMiOiI1ZTQ1M2Y0YzY5Yjc4OTI3ZDljMDk5MDM4Y2FhNTc4ZTAwNTk5ODJiNzExMWVhMDM0ZTM2ZDJjMzgxYjRmMmQ2In0%3D',
            'origin' => 'https://jobsearch.az',
            'referer' => 'https://jobsearch.az/',
            'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
            'x-requested-with' => 'XMLHttpRequest',
            // 'x-xsrf-token' => 'eyJpdiI6InRpWHdFanFHUE9nWWJOMUY0dnlGa0E9PSIsInZhbHVlIjoicFlzWVV6ZVZIdEw0R2dMK0YwQlUraENydWdHdEQxSlR0RFQ1QUVScnhjRnYxWUM4bnQ0alpCZXZPL0R2TDViQ1VROEFKV1RJS0Fja0lEOVhRNVNnRGlXQjBXcElyWjIreE9VSFVHUDN6MmJrMlNac1VwY0s0Ym1idTJkbEc5YWciLCJtYWMiOiI2Nzg4ZTk2ZmExMDI4MWY2ZWQ5M2I0YTZiNzNkMWI4MWJjNTdhOWJlM2U2YzIyZDBhMzc0MWRlOWM3YzRjNGYxIn0=',

        ])
            ->post('https://jobsearch.az/api-az/vacancies-az/'.$slug, [
                'hl' => 'az',
            ]);

        return $data->json();

    }
}
if (! function_exists('getCompanyInfoBySlug')) {

    function getCompanyInfoBySlug($slug)
    {
        $data = Http::withHeaders([
            'authority' => 'jobsearch.az',
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
            //'cookie' => 'user=%7B%22error%22%3Afalse%2C%22favorites_count%22%3A0%7D; _gcl_au=1.1.*********.1686317179; _ga=GA1.2.*********.1686317180; _gid=GA1.2.1333868943.1688149855; dark_mode=true; lang=az; XSRF-TOKEN=eyJpdiI6InRpWHdFanFHUE9nWWJOMUY0dnlGa0E9PSIsInZhbHVlIjoicFlzWVV6ZVZIdEw0R2dMK0YwQlUraENydWdHdEQxSlR0RFQ1QUVScnhjRnYxWUM4bnQ0alpCZXZPL0R2TDViQ1VROEFKV1RJS0Fja0lEOVhRNVNnRGlXQjBXcElyWjIreE9VSFVHUDN6MmJrMlNac1VwY0s0Ym1idTJkbEc5YWciLCJtYWMiOiI2Nzg4ZTk2ZmExMDI4MWY2ZWQ5M2I0YTZiNzNkMWI4MWJjNTdhOWJlM2U2YzIyZDBhMzc0MWRlOWM3YzRjNGYxIn0%3D; JOB_SEARCH=eyJpdiI6Ik91NW4zcjB5eEpHaUI4NXZWRXU2MXc9PSIsInZhbHVlIjoiTEZ5UGZreCtGQndzemdWVFpXQjRad1FEcHpxQ3NCRno2aTZ4ak82ZTFoOGpJcmlNUjdvdi85T2hOeWdMdDlmbktaSXFXc0ZpbXVjKzlXSnFWaTkrVXAweTNtc29rM3pPUWZUL2tjNS8wMG52VXpZWGVOcEYyem1xM3owTWZCQVgiLCJtYWMiOiI1ZTQ1M2Y0YzY5Yjc4OTI3ZDljMDk5MDM4Y2FhNTc4ZTAwNTk5ODJiNzExMWVhMDM0ZTM2ZDJjMzgxYjRmMmQ2In0%3D',
            'referer' => 'https://jobsearch.az/api-az/companies-az/'.$slug,
            'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
            'x-requested-with' => 'XMLHttpRequest',
            //'x-xsrf-token' => 'eyJpdiI6InRpWHdFanFHUE9nWWJOMUY0dnlGa0E9PSIsInZhbHVlIjoicFlzWVV6ZVZIdEw0R2dMK0YwQlUraENydWdHdEQxSlR0RFQ1QUVScnhjRnYxWUM4bnQ0alpCZXZPL0R2TDViQ1VROEFKV1RJS0Fja0lEOVhRNVNnRGlXQjBXcElyWjIreE9VSFVHUDN6MmJrMlNac1VwY0s0Ym1idTJkbEc5YWciLCJtYWMiOiI2Nzg4ZTk2ZmExMDI4MWY2ZWQ5M2I0YTZiNzNkMWI4MWJjNTdhOWJlM2U2YzIyZDBhMzc0MWRlOWM3YzRjNGYxIn0=',
        ])
            ->get('https://jobsearch.az/api-az/companies-az/'.$slug, [
                'hl' => 'az',
            ]);

        return $data->json();
    }

    function convertToMultiDimensionalArray($array): array
    {
        $result = [];
        foreach ($array as $key => $value) {
            foreach ($value['child'] as $childKey => $childValue) {
                $result[$childKey] = $value['parent'].str_ireplace('-', '/', $childValue);
            }
        }

        return $result;
    }

}
//job search .az

if (! function_exists('remoteCategoryByChannel')) {
    function remoteCategoryByChannel($r_category_id, $channel_id = 1)
    {
        return DB::table('category_channel')
            ->where('r_category_id', $r_category_id)
            ->where('channel_id', $channel_id)
            ->value('l_category_id');
    }
}
if (! function_exists('localCategoryByChannel')) {
    function localCategoryByChannel($l_category_id, $channel_id = 1)
    {
        return DB::table('category_channel')
            ->where('l_category_id', $l_category_id)
            ->where('channel_id', $channel_id)
            ->value('r_category_id');
    }
}
if (! function_exists('getJobListFromHelloJob')) {
    function getJobListFromHelloJob($url): array
    {
        mb_internal_encoding('UTF-8');
        $response = Http::withHeaders([
            'authority' => 'www.hellojob.az',
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
            'cache-control' => 'max-age=0',
            'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'none',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => 1,
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        ])
            ->get($url);

        if (! $response->successful()) {
            $links = [];
        } else {
            $html = $response->body();
            $dom = new DOMDocument;
            libxml_use_internal_errors(true);
            $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            // $dom->loadHTML($html);
            libxml_clear_errors();
            $xpath = new DOMXPath($dom);
            $links = [];
            $divClass = 'col-md-8';
            $excludeDivClass = 'banner';
            $divElements = $xpath->query("//div[contains(@class, '$divClass')]");
            foreach ($divElements as $divElement) {
                $aElements = $xpath->query(".//a[not(ancestor::div[contains(@class, '$excludeDivClass')])]", $divElement);
                foreach ($aElements as $aElement) {
                    $href = $aElement->getAttribute('href');
                    $links[] = $href;
                }
            }
            $links = array_filter($links, function ($link) {
                return str_contains($link, '/vakansiya/');
            });
            $links = array_unique($links);
            $links = array_values($links);
        }

        return $links;
    }
}
if (! function_exists('getCompanyFromHelloJob')) {

    function getCompanyFromHelloJob($url, $emailFromJob): array
    {
        mb_internal_encoding('UTF-8');
        $response = Http::withHeaders([
            'authority' => 'www.hellojob.az',
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
            'cache-control' => 'max-age=0',
            'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'none',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => 1,
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        ])
            ->get($url);

        if (! $response->successful()) {
            $company_detail = [];
        } else {
            $html = $response->body();
            $dom = new DOMDocument;
            $dom->encoding = 'UTF-8';
            libxml_use_internal_errors(true);
            $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            $xpath = new DOMXPath($dom);
            $divClass = 'company__head__top';
            $divElements = $xpath->query("//div[contains(@class, '$divClass')]");
            $backgroundImageUrl = '';
            foreach ($divElements as $li) {
                $backgroundImage = $li->getAttribute('style');
                preg_match('/url\((.*?)\)/', $backgroundImage, $backgroundImageMatches);
                $backgroundImageUrl = $backgroundImageMatches[1] ?? '';
            }
            $imagesLazyLoading = '';
            $imgElements = $dom->getElementsByTagName('img');
            foreach ($imgElements as $imgElement) {
                $loadingAttributeValue = $imgElement->getAttribute('loading');
                if ($loadingAttributeValue === 'lazy') {
                    $src = $imgElement->getAttribute('src');
                    if (str_contains($src, 'https://www.hellojob.az/uploads/company/')) {
                        $imagesLazyLoading = $src;
                    }
                }
            }
            $divClass_content = 'content';
            $divElements_content = $xpath->query("//div[contains(@class, '$divClass_content')]")->item(0);
            if ($divElements_content) {
                $company_content = $divElements_content->nodeValue ?? '';
            } else {
                $company_content = '';
            }
            $emailNode = $xpath->query('//h4[@class="hidd_email d-none"]')->item(0);
            $email = $emailNode->textContent ?? $emailFromJob;

            $companyHeadName = $xpath->query('//h2[@class="company__head__name"]')->item(0);
            $companyName = $companyHeadName ? $companyHeadName->nodeValue : '';

            $urlNode = $xpath->query('//div[@class="resume__item__text"]/h4/a')->item(0);
            if ($urlNode) {
                $url_parse = $urlNode->getAttribute('href');
            } else {
                $url_parse = '';
            }
            $company_detail = [
                'backgroundImageUrl' => $backgroundImageUrl,
                'imagesLazyLoading' => $imagesLazyLoading,
                'company_content' => $company_content,
                'email' => $email,
                'url_parse' => $url_parse,
                'company_url' => $url,
                'company_name' => $companyName,
            ];

        }

        return $company_detail;
    }

}
if (! function_exists('getJobDetailFromHelloJob')) {
    function getJobDetailFromHelloJob($url)
    {
        mb_internal_encoding('UTF-8');
        $response = Http::withHeaders([
            'authority' => 'www.hellojob.az',
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
            'cache-control' => 'max-age=0',
            'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'none',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => 1,
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        ])
            ->get($url);

        if (! $response->successful()) {
            \Log::error('Failed to fetch job details from HelloJob', [
                'url' => $url,
                'status' => $response->status(),
                'error' => $response->body()
            ]);
            return [];
        } else {
            $html = $response->body();
            $dom = new DOMDocument;
            $dom->encoding = 'UTF-8';
            libxml_use_internal_errors(true);
            $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            libxml_clear_errors();
            $xpath = new DOMXPath($dom);
            // Find the <h3> element with class "resume__header__name"
            $nameElement = $xpath->query("//h3[@class='resume__header__name']")->item(0);
            $name = $nameElement ? trim($nameElement->nodeValue) : '';
            $name = str_ireplace('is elani', '', $name);
            $name = str_ireplace('iş elanı', '', $name);
            $name = trim($name);

            // If name is still empty, try alternative selectors
            if (empty($name)) {
                $titleElement = $xpath->query("//title")->item(0);
                if ($titleElement) {
                    $pageTitle = $titleElement->nodeValue;
                    // Extract job title from page title
                    $name = preg_replace('/\s*vakansiyası.*$/i', '', $pageTitle);
                    $name = preg_replace('/\s*-\s*İş elanları.*$/i', '', $name);
                    $name = trim($name);
                }
            }

            // If still empty, try h1 tag
            if (empty($name)) {
                $h1Element = $xpath->query("//h1")->item(0);
                if ($h1Element) {
                    $name = trim($h1Element->nodeValue);
                    $name = str_ireplace('is elani', '', $name);
                    $name = str_ireplace('iş elanı', '', $name);
                    $name = trim($name);
                }
            }
            // Find the <a> element within the <p> element with class "resume__header__speciality"
            $aElement = $xpath->query("//p[@class='resume__header__speciality']/a")->item(0);
            if ($aElement) {
                $href = $aElement->getAttribute('href');
                $speciality = trim($aElement->nodeValue);
            } else {
                $href = '';
                $speciality = '';
            }

            $jobDetail = [
                'name' => $name,
                'speciality' => $speciality,
                'href' => $href,
            ];

            // \App\Models\Company::

            /*ToDo*/

            // Find the <h3> element with class "resume__title" containing job details
            $titleElement = $xpath->query("//h3[@class='resume__title']")->item(0);
            // Find all <div> elements with class "resume__item__text"
            $itemTextElements = $xpath->query("//div[@class='resume__item__text']");

            $jobDetails = [];
            foreach ($itemTextElements as $itemTextElement) {
                // Extract the job information
                $h4Element = $itemTextElement->getElementsByTagName('h4')->item(0);
                $pElement = $itemTextElement->getElementsByTagName('p')->item(0);

                if ($h4Element && $pElement) {
                    $jobTitle = trim($h4Element->nodeValue);
                    $jobCategory = trim($pElement->nodeValue);

                    // Add the job information to the array
                    $jobDetails[] = [
                        'key' => $jobTitle,
                        'value' => $jobCategory,
                    ];
                }
            }

            // Output the parsed values
            foreach ($jobDetails as $job) {
                $key = Str::slug($job['value']);
                $value = trim($job['key']);
                $jobDetail['jobDetails'][$key] = $value;

                // Map common field names to expected keys
                if (str_contains(strtolower($job['value']), 'sahə') || str_contains(strtolower($job['value']), 'field')) {
                    $jobDetail['jobDetails']['sahe'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'kateqoriya') || str_contains(strtolower($job['value']), 'category')) {
                    $jobDetail['jobDetails']['kateqoriya'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'təcrübə') || str_contains(strtolower($job['value']), 'experience')) {
                    $jobDetail['jobDetails']['is-staji'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'iş rejimi') || str_contains(strtolower($job['value']), 'work mode')) {
                    $jobDetail['jobDetails']['is-rejimi'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'əmək haqqı') || str_contains(strtolower($job['value']), 'salary')) {
                    $jobDetail['jobDetails']['emek-haqqi-azn'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'baxılıb') || str_contains(strtolower($job['value']), 'views')) {
                    $jobDetail['jobDetails']['baxilib'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'e-poçt') || str_contains(strtolower($job['value']), 'email')) {
                    $jobDetail['jobDetails']['e-poct-unvani'] = $value;
                }
                if (str_contains(strtolower($job['value']), 'müraciət linki') || str_contains(strtolower($job['value']), 'apply link')) {
                    $jobDetail['jobDetails']['muraciet-linki'] = $value;
                }
            }
            //apply

            $applyLinkElement = $dom->getElementsByTagName('a');
            foreach ($applyLinkElement as $link) {
                $classes = $link->getAttribute('class');
                if (str_contains($classes, 'btn btn-apply')) {
                    $applyText = $link->textContent;
                    $href = $link->getAttribute('href');
                    $nodeValue = $link->nodeValue;

                    $jobDetail['jobDetails']['applyData'] = [
                        'applyText' => trim($applyText),
                        'href' => $href,
                        'nodeValue' => Str::slug(trim($nodeValue)),
                    ];
                    break;
                }
            }

            // Find the <h3> element with class "resume__title" containing job details
            $titleElement = $xpath->query("//h3[@class='resume__title']")->item(1);

            // Find the <h3> element with class "resume__title"
            $h3Element = $xpath->query('//h3[@class="resume__title"]')->item(1);

            $h3Element?->parentNode->removeChild($h3Element);

            // Select the specific <div> with class "resume__block"
            $selectedDiv = $xpath->query('//div[@class="resume__block"]')->item(0);

            if ($selectedDiv) {
                $nextDiv = $selectedDiv->nextSibling;

                //TODO: check if next div is not null
                while ($nextDiv && $nextDiv->nodeName !== 'div') {
                    $nextDiv = $nextDiv->nextSibling;
                }

                if ($nextDiv) {
                    $content = str_ireplace('Vakansiyalardan daha tez xəbərdar olmaq üçün Telegram kanalımıza abunə olun!', '', $nextDiv->textContent);
                    $jobDetail['jobDetails']['job_info'] = trim($content);
                } else {
                    $jobDetail['jobDetails']['job_info'] = '';
                }
            } else {
                $jobDetail['jobDetails']['job_info'] = '';
            }

            //job block
            $titleElement = $xpath->query('//div[@class="resume__block"]')->item(1);

            if ($titleElement) {
                // Find the <h3> element with class "resume__title"
                $h3Element = $xpath->query('//h3[@class="resume__title"]')->item(1);

                $h3Element?->parentNode->removeChild($h3Element);

                $nextDiv = $titleElement->nextSibling;
                while ($nextDiv && $nextDiv->nodeName !== 'div') {
                    $nextDiv = $nextDiv->nextSibling;
                }

                if ($nextDiv) {
                    $nextDivContent = str_ireplace('Vakansiyalardan daha tez xəbərdar olmaq üçün Telegram kanalımıza abunə olun!', '', $nextDiv->textContent);

                    if (str_contains($nextDivContent, 'Diqqət! Heç bir halda işəgötürənin sizdən ödəniş tələbinə əməl etməyin!')) {
                        $nextDivContent = '';
                    }

                    if (str_contains($nextDivContent, 'Müraciət linki') || str_contains($nextDivContent, 'Müraciət üçün link') || str_contains($nextDivContent, 'Linki kopyala')) {
                        $nextDivContent = '';
                    }

                    $jobDetail['jobDetails']['job_required'] = trim($nextDivContent);
                } else {
                    $jobDetail['jobDetails']['job_required'] = '';
                }
            } else {
                $jobDetail['jobDetails']['job_required'] = '';
            }

            $h4_elements = $dom->getElementsByTagName('h4');

            foreach ($h4_elements as $h4) {
                $h4_content = $h4->nodeValue;
                if (preg_match('/Elan № (\d+)/', $h4_content, $matches)) {
                    $jobDetail['jobDetails']['job_number'] = $matches[1];
                }
            }

            // Add fallback values for required fields if not found
            if (!isset($jobDetail['jobDetails']['sahe'])) {
                $jobDetail['jobDetails']['sahe'] = 'Müxtəlif';
            }
            if (!isset($jobDetail['jobDetails']['kateqoriya'])) {
                $jobDetail['jobDetails']['kateqoriya'] = 'Digər';
            }

        }

        return $jobDetail;
    }
}
if (! function_exists('insertCompanyAndUser')) {
    function insertCompanyAndUser($url, $email)
    {
        try {
            $getJobCompany = getCompanyFromHelloJob($url, $email);
            if (empty($getJobCompany)) {
                \Log::warning('No company data retrieved', ['url' => $url, 'email' => $email]);
                return null;
            }
            /* @User $user */
            if (! User::query()->where('email', $getJobCompany['email'])->first('id')) {
                $logo = $getJobCompany['imagesLazyLoading'];
                $banner = $getJobCompany['backgroundImageUrl'];
                $path = 'uploads/company/';
                if(public_path($path) && !file_exists(public_path($path))) {
                    mkdir(public_path($path), 0777, true);
                }
                if ($logo != '' && $banner != '') {
                    $logo_name = basename($logo);
                    $banner_name = basename($banner);
                    $logo_path = public_path($path.$logo_name);
                    $banner_path = public_path($path.$banner_name);
                    file_put_contents($logo_path, file_get_contents($logo));
                    file_put_contents($banner_path, file_get_contents($banner));

                    $logo_name = $path.$logo_name;
                    $banner_name = $path.$banner_name;

                } else {
                    $logo_name = 'backend/image/default.png';
                    $banner_name = 'backend/image/default.png';
                }

                $user = User::query()->create([
                    'name' => $getJobCompany['company_name'],
                    'username' => Str::slug($getJobCompany['company_name']),
                    'email' => $getJobCompany['email'],
                    'password' => Hash::make('12345678'),
                    'image' => $logo_name,
                    'role' => 'company',
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                //create company
                $user->company()->update([
                    'industry_type_id' => 1,
                    'organization_type_id' => 1,
                    'team_size_id' => 1,
                    'logo' => $logo_name,
                    'banner' => $banner_name,
                    'establishment_date' => now(),
                    'website' => $getJobCompany['url_parse'] ?? 'projob.az',
                    'visibility' => 1,
                    'profile_completion' => 1,
                    'bio' => $getJobCompany['company_content'] ?? '',
                    'vision' => $getJobCompany['company_content'] ?? '',
                    'total_views' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'address' => '',
                    'neighborhood' => '',
                    'locality' => '',
                    'place' => '',
                    'district' => '',
                    'postcode' => '',
                    'region' => '',
                    'country' => 'Azerbaijan',
                    'long' => '49',
                    'lat' => '40',
                    'slug' => Str::slug($getJobCompany['company_name']),
                ]);

            }

            $user = User::query()->where('email', $getJobCompany['email'])->first('id');

            return $user->company->id;
        } catch (\Exception $e) {
            \Log::error('Error in insertCompanyAndUser', [
                'error' => $e->getMessage(),
                'url' => $url,
                'email' => $email
            ]);
            return null;
        }
    }

}
if (! function_exists('getCompanyId')) {
    function getCompanyId($data = null)
    {
        if (! empty($data)) {
            $category = User::where('name', 'LIKE', "%$data%")
                ->orWhere('username', 'LIKE', "%$data%")
                ->first();

            if ($category) {
                return Company::query()->where('user_id', $category->id)->first('id')?->id;
            }
        }

        return 0;
    }
}
if (! function_exists('getCategoryId')) {
    function getCategoryId($data = null)
    {
        if (! empty($data)) {
            $category = JobCategoryTranslation::where('name', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($category) {
                return $category->job_category_id;
            }
        }

        return JobCategory::inRandomOrder()->value('id');
    }
}
if (! function_exists('getExperienceId')) {
    function getExperienceId($data = null)
    {
        if (! empty($data)) {
            $experience = Experience::query()->where('slug', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($experience) {
                return $experience->id;
            }
        }

        return Experience::query()->inRandomOrder()->value('id');
    }
}
if (! function_exists('getEducationId')) {
    function getEducationId($data = null)
    {
        if (! empty($data)) {
            $education = Education::where('name', 'LIKE', "%$data%")
                ->orWhere('slug', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($education) {
                return $education->id;
            }
        }

        return Education::inRandomOrder()->value('id');
    }
}
if (! function_exists('getJobTypeId')) {
    function getJobTypeId($data = null)
    {
        if (! empty($data)) {
            $job_type = JobType::where('name', 'LIKE', "%$data%")
                ->orWhere('slug', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($job_type) {
                return $job_type->id;
            }
        }

        return JobType::inRandomOrder()->value('id');
    }
}
if (! function_exists('getJobRoleId')) {
    function getJobRoleId($data = null)
    {
        if (! empty($data)) {
            $role = JobRoleTranslation::where('name', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($role) {
                return $role->job_role_id;
            }
        }

        return JobRole::inRandomOrder()->value('id');
    }
}
if (! function_exists('getSalaryTypeId')) {
    function getSalaryTypeId($data = null)
    {
        if (! empty($data)) {
            $salary_type = SalaryType::where('name', 'LIKE', "%$data%")
                ->orWhere('slug', 'LIKE', "%$data%")
                ->orWhere('id', 'LIKE', "%$data%")
                ->first();

            if ($salary_type) {
                return $salary_type->id;
            }
        }

        return SalaryType::inRandomOrder()->value('id');
    }
}
if (! function_exists('jobsImport')) {
    function jobsImport($jobs)
    {
        // Validate input data
        if (empty($jobs) || !isset($jobs['name']) || !isset($jobs['jobDetails'])) {
            \Log::warning('Invalid job data provided to jobsImport', ['data' => $jobs]);
            return null;
        }

        $job_number = $jobs['jobDetails']['job_number'] ?? null;

        // Skip if job already exists
        if ($job_number && Job::query()->where('job_number', $job_number)->exists()) {
            \Log::info('Job already exists, skipping', ['job_number' => $job_number]);
            return null;
        }
            $title = $jobs['name'];
            $category_id = getCategoryId($jobs['jobDetails']['sahe'].' / '.$jobs['jobDetails']['kateqoriya']);
            $description = $jobs['jobDetails']['job_info'] ?? '';
            $description .= isset($jobs['jobDetails']['job_required']) ?: '<br>'.$jobs['jobDetails']['job_required'];
            $job_role_id = 7;
            $salary_type_id = 1;
            $experience_id = 1;
            $job_number = $jobs['jobDetails']['job_number'] ?? 0;

            if (isset($jobs['jobDetails']['is-staji'])) {
                $experience_id = getExperienceId($jobs['jobDetails']['is-staji']);
            }
            $job_type_id = 2;
            if (isset($jobs['jobDetails']['is-is-rejimi'])) {
                $job_type_id = getJobTypeId($jobs['jobDetails']['is-rejimi']);
            }

            $total_views = $jobs['jobDetails']['baxilib'] ?? 0;
            $education_id = 7;
            $vacancies = 1;
            $min_salary = 0;
            $max_salary = 0;
            $custom_salary = null;
            $salary_mode = 'custom';

            $apply_email = null;
            $apply_url = null;
            if (isset($jobs['jobDetails']['e-poct-unvani'])) {
                $apply_on = 'email';
                $apply_email = $jobs['jobDetails']['applyData']['applyText'];
            } elseif (isset($jobs['jobDetails']['muraciet-linki'])) {
                $apply_on = 'custom_url';
                $apply_url = $jobs['jobDetails']['applyData']['href'];

                if (! filter_var(gethostbyname($apply_url), FILTER_VALIDATE_IP)) {
                    $apply_on = 'app';
                }

            } else {
                $apply_on = 'app';
            }

            try {
                $company_url = $jobs['href'];
                if (!str_starts_with($company_url, 'http')) {
                    $company_url = 'https://www.hellojob.az' . $company_url;
                }
                $company_id = insertCompanyAndUser($company_url, $apply_email ?? Str::slug($jobs['speciality']).'@projob.az');

                if (!$company_id) {
                    \Log::error('Failed to create or find company', ['href' => $jobs['href']]);
                    return null;
                }
            } catch (\Exception $e) {
                \Log::error('Error creating company', ['error' => $e->getMessage(), 'href' => $jobs['href']]);
                return null;
            }

            if (isset($jobs['jobDetails']['emek-haqqi-azn'])) {
                $salary = explode('-', $jobs['jobDetails']['emek-haqqi-azn']);
                $min_salary = trim(str_ireplace('azn', '', trim($salary[0])));
                if (isset($salary[1])) {
                    $salary_mode = 'range';
                    $max_salary = trim(str_ireplace('azn', '', trim($salary[1])));
                } else {
                    $min_salary = 0;
                    $custom_salary = trim(str_ireplace('azn', '', trim($salary[0])));
                }
            }

            $deadline = Carbon::now()->addDays(30)->format('Y-m-d');

            $insert_data = [
                'title' => $title,
                'slug' => Str::slug($title).'-'.Job::query()->max('id') + 1,
                'company_id' => $company_id,
                'company_name' => $jobs['speciality'] ?? '',
                'category_id' => $category_id,
                'role_id' => $job_role_id,
                'experience_id' => $experience_id,
                'education_id' => $education_id,
                'job_type_id' => $job_type_id,
                'salary_type_id' => $salary_type_id,
                'vacancies' => $vacancies,
                'min_salary' => $min_salary,
                'max_salary' => $max_salary,
                'custom_salary' => $custom_salary,
                'salary_mode' => $salary_mode,
                'deadline' => Carbon::parse($deadline)->format('Y-m-d'),
                'description' => $description,
                'total_views' => $total_views,
                'is_remote' => 0,
                'status' => 'active',
                'featured' => 0,
                'highlight' => 0,
                'apply_on' => $apply_on,
                'apply_email' => $apply_email ?? '',
                'apply_url' => $apply_url ?? '',
                'country' => 'Azerbaijan',
                'exact_location' => 'Azerbaijan',
                'lat' => '40',
                'long' => '49',
                'job_number' => $job_number,
            ];

            try {
                $job = Job::create($insert_data);
                \Log::info('Job created successfully', ['job_id' => $job->id, 'job_number' => $job_number]);
                return $job;
            } catch (\Exception $e) {
                \Log::error('Failed to create job', [
                    'error' => $e->getMessage(),
                    'job_data' => $insert_data,
                    'job_number' => $job_number
                ]);
                return null;
            }
    }
}
