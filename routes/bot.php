<?php

ini_set('max_execution_time', -1);
set_time_limit(0);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

Route::get('/getCat', function () {

    $locale = 'en';
    $page = 2;

    $guzzle = Http::withHeaders([
        'authority' => 'jobsearch.az',
        'accept' => 'application/json, text/plain, */*',
        'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
        'cookie' => 'user=%7B%22error%22%3Afalse%2C%22favorites_count%22%3A0%7D; _gcl_au=1.1.710087776.1686317179; _ga=GA1.2.327053092.1686317180; _gid=GA1.2.1333868943.1688149855; lang=az; XSRF-TOKEN=eyJpdiI6InBLWXkvWU1kRitpQ2g4bkpaVnErNmc9PSIsInZhbHVlIjoicWNoQ3BzSjNYdFZRa0p0dHlDMXZBTUlSS2lMc1J3T2Q4ZmpSdUxrR3Z2dHpERlQ3M25KT0lTUDNhRXl2NGk5cEErWTJLRGtETDdUMWFYdW9TcXRING1mL1o2dmpEaVJoeUMzOS9SZGtnZVMrS0ZLenFtSXMxU2ttN3hsUVR2blciLCJtYWMiOiI0ZTk3Zjc0MWEyNTk0NzFjNzQ2M2JlYTA5MDU2NjA4YzNlY2IyYzBlZjgxZTA3YThkNWViNWFkM2RmNGU4OGZhIn0%3D; JOB_SEARCH=eyJpdiI6IkVvTVREbVJOMXhsOGh3VThGVFNaTmc9PSIsInZhbHVlIjoiaFFTZ2ZXZWRXZU80MTBwUXRjUEF3RjNUYmhBZ05zdjZ5SFRGSWZlclNVZVgzY2RnSCt3R0dJRnUxajFpN09mNEU0a2hTRWR6SnE1ZWFkM2NVU3cxaWhZR0xLcEsxMDZHaWJIRFQ3TnpnUGFRZkJ0b2RtcWtOOWUxS1Nibjg3S0IiLCJtYWMiOiI0YmU0NGQxYTYxZDlkMzMxMWVmM2FiMzhmNWFiNzRjZjYwZGI5MzRlMWViOWI5ZDlkMGQyMDA2ZTA1ZjBjOTcyIn0%3D; _gat_UA-208005358-1=1',
        'referer' => 'https://jobsearch.az/categories',
        'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'empty',
        'sec-fetch-mode' => 'cors',
        'sec-fetch-site' => 'same-origin',
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        'x-requested-with' => 'XMLHttpRequest',
        'x-xsrf-token' => 'eyJpdiI6InBLWXkvWU1kRitpQ2g4bkpaVnErNmc9PSIsInZhbHVlIjoicWNoQ3BzSjNYdFZRa0p0dHlDMXZBTUlSS2lMc1J3T2Q4ZmpSdUxrR3Z2dHpERlQ3M25KT0lTUDNhRXl2NGk5cEErWTJLRGtETDdUMWFYdW9TcXRING1mL1o2dmpEaVJoeUMzOS9SZGtnZVMrS0ZLenFtSXMxU2ttN3hsUVR2blciLCJtYWMiOiI0ZTk3Zjc0MWEyNTk0NzFjNzQ2M2JlYTA5MDU2NjA4YzNlY2IyYzBlZjgxZTA3YThkNWViNWFkM2RmNGU4OGZhIn0=',
    ])
        ->get('https://jobsearch.az/api-az/categories-'.$locale, [
            'q' => '',
            'hl' => $locale,
            'page' => $page,
        ]);

    $json = json_decode($guzzle->body(), true);

    $new = [];
    foreach ($json['items'] as $key => $value) {
        $new[$value['id']] = $value['title'];
    }

    foreach ($new as $key => $value) {

        if (! DB::table('category_channel')->where('r_category_id', $key)->exists()) {

            $id = DB::table('job_categories')->insertGetId([
                'image' => 'backend/image/default.png',
                'icon' => 'fas fa-pen',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('category_channel')->insert([
                'r_category_id' => $key,
                'l_category_id' => $id,
                'channel_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('job_category_translations')->insert([
                'job_category_id' => $id,
                'name' => $value,
                'locale' => $locale,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $id = DB::table('category_channel')->where('r_category_id', $key)->first();

            if (! DB::table('job_category_translations')->where('job_category_id', $id->l_category_id)->where('locale', $locale)->exists()) {
                DB::table('job_category_translations')->insert([
                    'job_category_id' => $id->l_category_id,
                    'name' => $value,
                    'locale' => $locale,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

        }

        echo $value.' - '.$key.'<br>';

    }

});
Route::get('/getLast', function () {
    $guzzle = Http::withHeaders([
        'authority' => 'jobsearch.az',
        'accept' => 'application/json, text/plain, */*',
        'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
        //'cookie' => 'user=%7B%22error%22%3Afalse%2C%22favorites_count%22%3A0%7D; _gcl_au=1.1.710087776.1686317179; _ga=GA1.2.327053092.1686317180; _gid=GA1.2.1333868943.1688149855; lang=az; dark_mode=true; XSRF-TOKEN=eyJpdiI6IitLSjZaMjlQVkhDaFdTblUrUzZJY0E9PSIsInZhbHVlIjoiR1pLU1ZwNDRvK0dwbnplUWZ1eVU1dmh6Qy9ycTJzM0x2Q3grNmpWZm0wWFZNaytIWTNod3ZpY2YvL3JnNCtpRDNBYitLNkQvdldkaG42Qk9kTjA5SDZhL1JEUkdtMWtISmFBSVpLKzlzNENCNUVySDdvZHFuZHdJVnBxREhmR0EiLCJtYWMiOiJhNThmYjg0NmJjNjhmNDU1YTgwNzgzNzI0YTVmMjBmMzE1YmU5NWI4NDMzMTU4YjBjNWY3NjdkOGIzOGNjNDI4In0%3D; JOB_SEARCH=eyJpdiI6ImQzMyt0a3pOdFZFSlVoUHBEV0ovOWc9PSIsInZhbHVlIjoiU3o1VFFhNFJrQzd5TldURGllLzE4QlNaV0MrYkVjTVJsTzVYNWV4U1FQcVV2Q0RmNlUzUEZsaklzK3hQUVV5cU52TjVmWlBFdWVIZXBqWVBkcDZ1aFdRZDR1NmZoZ25tNytRc0ZBV0FCWldSMWNKNS9HTzJFSC9NSTZhbUpJSjQiLCJtYWMiOiI5Y2Y3OGJjZGZhZTA5NzdjMjVlMmU2NjY3ODY3NjJmNzZlNzA4YzE4ODNjOTJiN2QyNjY1ZDhhYTY0OTc5ZmYzIn0%3D',
        'referer' => 'https://jobsearch.az/vacancies',
        'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'empty',
        'sec-fetch-mode' => 'cors',
        'sec-fetch-site' => 'same-origin',
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        'x-requested-with' => 'XMLHttpRequest',
        //'x-xsrf-token' => 'eyJpdiI6IitLSjZaMjlQVkhDaFdTblUrUzZJY0E9PSIsInZhbHVlIjoiR1pLU1ZwNDRvK0dwbnplUWZ1eVU1dmh6Qy9ycTJzM0x2Q3grNmpWZm0wWFZNaytIWTNod3ZpY2YvL3JnNCtpRDNBYitLNkQvdldkaG42Qk9kTjA5SDZhL1JEUkdtMWtISmFBSVpLKzlzNENCNUVySDdvZHFuZHdJVnBxREhmR0EiLCJtYWMiOiJhNThmYjg0NmJjNjhmNDU1YTgwNzgzNzI0YTVmMjBmMzE1YmU5NWI4NDMzMTU4YjBjNWY3NjdkOGIzOGNjNDI4In0=',
    ])
        ->get('https://jobsearch.az/api-az/vacancies-az', [
            'hl' => 'az',
            'q' => '',
            'posted_date' => '',
            'seniority' => '',
            'categories' => '850',
            'industries' => '1637',
            'ads' => '',
            'location' => '',
            'job_type' => '',
            'salary' => '',
            'order_by' => '',
        ]);

    $json = json_decode($guzzle->body(), true);

    $new = [];
    foreach ($json['items'] as $key => $value) {
        $new[$value['id']] = $value;
    }

    dd($new);

    try {
        foreach ($new as $key => $value) {

            DB::beginTransaction();

            if (! DB::table('jobs')->where('slug', $value['slug'])->exists()) {
                // dd($value);

                if ($value['request_type'] == 'show_email') {
                    //TODO: show email
                    //dd($value);
                }

                $company_id = 0;

                if (! DB::table('companies')->where('slug', $value['company']['slug'])->exists()) {

                    $logo = $value['company']['logo'];
                    $banner = $value['company']['logo_mini'];
                    $path = 'uploads/company/';
                    if ($logo != '' && $banner != '') {
                        $logo_name = basename($logo);
                        $banner_name = basename($banner);
                        $logo_path = public_path($path.$logo_name);
                        $banner_path = public_path($path.$banner_name);
                        file_put_contents($logo_path, file_get_contents($logo));
                        file_put_contents($banner_path, file_get_contents($banner));
                    } else {
                        $logo_name = 'default.png';
                        $banner_name = 'default.png';
                    }

                    //
                    $detail_info = getCompanyInfoBySlug($value['company']['slug']);
                    $user_id = DB::table('users')->insertGetId([
                        'name' => $detail_info['title'],
                        'email' => $detail_info['slug'].'@'.$detail_info['sites'][0]['title'] ?? 'projob.az',
                        'email_verified_at' => now(),
                        'password' => Hash::make('12345678'),
                        'role' => 'company',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    DB::table('contact_infos')->insert([
                        'user_id' => $user_id,
                        'phone' => $detail_info['phones'][0] ?? '',
                        'secondary_phone' => $detail_info['phones'][1] ?? '',
                        'email' => $detail_info['slug'].'@'.$detail_info['sites'][0]['title'] ?? '',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $company_id = DB::table('companies')
                        ->insertGetId([
                            'user_id' => $user_id,
                            'industry_type_id' => 1,
                            'organization_type_id' => 1,
                            'team_size_id' => 1,
                            'logo' => $path.$logo_name,
                            'banner' => $path.$banner_name,
                            'establishment_date' => now(),
                            'website' => $detail_info['sites'][0]['url'] ?? 'projob.az',
                            'visibility' => 1,
                            'profile_completion' => 1,
                            'bio' => $detail_info['text'] ?? '',
                            'vision' => $detail_info['summary'] ?? '',
                            'total_views' => 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                            'address' => $detail_info['address'] ?? '',
                            'neighborhood' => '',
                            'locality' => '',
                            'place' => '',
                            'district' => '',
                            'postcode' => '',
                            'region' => '',
                            'country' => 'Azerbaijan',
                            'long' => $detail_info['coordinates']['lng'] ?? '49',
                            'lat' => $detail_info['address']['lat'] ?? '40',
                            'slug' => $detail_info['slug'],
                        ]);

                }

                $company_id = DB::table('companies')->where('slug', $value['company']['slug'])->first()->id;

                $category_id = remoteCategoryByChannel($value['category']['id']);
                //Todo: category

                DB::commit();

                //   dd('Ramil slug', $value['slug']);

                $xx = getCompanyJobBySlug($value['slug']);

                dd('Ramil', $xx);

            }

        }

    } catch (\Exception $e) {
        DB::rollBack();
        dd($e->getMessage());
    }

});
Route::get('/get-cat-hellojob', function () {

    $html = '<ul class="chosen-results"><li class="active-result result-selected" data-option-array-index="0">Kateqoriya</li><li class="active-result bg-primary text-white" data-option-array-index="1">Maliyyə</li><li class="active-result" data-option-array-index="2"> - Kredit mütəxəssisi</li><li class="active-result" data-option-array-index="3"> - Mühasibat</li><li class="active-result" data-option-array-index="4"> - Maliyyə analiz</li><li class="active-result" data-option-array-index="5"> - Bank xidməti</li><li class="active-result" data-option-array-index="6"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="7">Marketinq</li><li class="active-result" data-option-array-index="8"> - Rəqəmsal marketinq</li><li class="active-result" data-option-array-index="9"> - Marketinq menecment</li><li class="active-result" data-option-array-index="10"> - SMM</li><li class="active-result" data-option-array-index="11"> - SEO/SEM</li><li class="active-result" data-option-array-index="12"> - Kopiraytinq</li><li class="active-result" data-option-array-index="13"> - İctimaiyyətlə əlaqələr (PR)</li><li class="active-result" data-option-array-index="14"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="15">Texnologiya</li><li class="active-result" data-option-array-index="16"> - Proqramlaşdırma</li><li class="active-result" data-option-array-index="17"> - Sistem idarəetməsi</li><li class="active-result" data-option-array-index="18"> - İT mütəxəssisi / məsləhətçi</li><li class="active-result" data-option-array-index="19"> - Texniki avadanlıq mütəxəssisi</li><li class="active-result" data-option-array-index="20"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="21">Satış</li><li class="active-result" data-option-array-index="22"> - Satış üzrə mütəxəssis</li><li class="active-result" data-option-array-index="23"> - Daşınmaz əmlak agenti / makler</li><li class="active-result" data-option-array-index="24"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="25">Xidmət</li><li class="active-result" data-option-array-index="26"> - Operator</li><li class="active-result" data-option-array-index="27"> - Logistika</li><li class="active-result" data-option-array-index="28"> - Kuryer</li><li class="active-result" data-option-array-index="29"> - SPA və gözəllik</li><li class="active-result" data-option-array-index="30"> - Xadimə</li><li class="active-result" data-option-array-index="31"> - Anbardar</li><li class="active-result" data-option-array-index="32"> - Restoran işi</li><li class="active-result" data-option-array-index="33"> - Sürücü</li><li class="active-result" data-option-array-index="34"> - Dayə</li><li class="active-result" data-option-array-index="35"> - Telefon ustası</li><li class="active-result" data-option-array-index="36"> - Fəhlə</li><li class="active-result" data-option-array-index="37"> - Turizm və mehmanxana işi</li><li class="active-result" data-option-array-index="38"> - Tərcüməçi</li><li class="active-result" data-option-array-index="39"> - Mühafizə xidməti</li><li class="active-result" data-option-array-index="40"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="41">Dizayn</li><li class="active-result" data-option-array-index="42"> - 3D modelləşdirmə</li><li class="active-result" data-option-array-index="43"> - Animasiya dizaynı</li><li class="active-result" data-option-array-index="44"> - İllustrasiya</li><li class="active-result" data-option-array-index="45"> - Qrafik dizayn</li><li class="active-result" data-option-array-index="46"> - Veb-dizayn</li><li class="active-result" data-option-array-index="47"> - Memar / İnteryer dizaynı</li><li class="active-result" data-option-array-index="48"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="49">Müxtəlif</li><li class="active-result" data-option-array-index="50"> - Sığorta</li><li class="active-result" data-option-array-index="51"> - Menecment</li><li class="active-result" data-option-array-index="52"> - Geologiya və ətraf mühit</li><li class="active-result" data-option-array-index="53"> - Mühəndis</li><li class="active-result" data-option-array-index="54"> - Tikinti</li><li class="active-result" data-option-array-index="55"> - Avtomatlaşdırılmış idarəetmə</li><li class="active-result" data-option-array-index="56"> - Fotoqrafiya</li><li class="active-result" data-option-array-index="57"> - Jurnalistika</li><li class="active-result" data-option-array-index="58"> - Tələbələr üçün</li><li class="active-result" data-option-array-index="59"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="60">Səhiyyə</li><li class="active-result" data-option-array-index="61"> - Həkim</li><li class="active-result" data-option-array-index="62"> - Tibb təmsilçisi</li><li class="active-result" data-option-array-index="63"> - Tibbi personal</li><li class="active-result" data-option-array-index="64"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="65">Təhsil və elm</li><li class="active-result" data-option-array-index="66"> - Məktəb tədrisi</li><li class="active-result" data-option-array-index="67"> - Universitet tədrisi</li><li class="active-result" data-option-array-index="68"> - Repetitor</li><li class="active-result" data-option-array-index="69"> - Xüsusi təhsil/ Təlim</li><li class="active-result" data-option-array-index="70"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="71">Sənaye və k/t</li><li class="active-result" data-option-array-index="72"> - Kənd təsərrüfatı</li><li class="active-result" data-option-array-index="73"> - Digər</li><li class="active-result bg-primary text-white" data-option-array-index="74">Hüquq</li><li class="active-result" data-option-array-index="75"> - Hüquqşünas</li><li class="active-result" data-option-array-index="76"> - Cinayət hüququ</li><li class="active-result" data-option-array-index="77"> - Vəkil</li><li class="active-result bg-primary text-white" data-option-array-index="78">İnzibati</li><li class="active-result" data-option-array-index="79"> - Heyətin idarəolunması</li><li class="active-result" data-option-array-index="80"> - Ofis menecmenti</li><li class="active-result" data-option-array-index="81"> - Katibə / Resepşn</li><li class="active-result" data-option-array-index="82"> - Digər</li></ul>';

    mb_internal_encoding('UTF-8');

    $dom = new DOMDocument;
    $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

    $xpath = new DOMXPath($dom);

    $categories = [];
    $parentId = '';

    $liElements = $xpath->query("//li[contains(@class, 'active-result')]");
    foreach ($liElements as $li) {
        $index = $li->getAttribute('data-option-array-index');
        $value = $li->nodeValue;

        if (str_contains($li->getAttribute('class'), 'bg-primary text-white')) {
            $parentId = $index;
            $categories[$parentId] = [
                'parent' => $value,
                'child' => [],
            ];
        } else {
            $categories[$parentId]['child'][$index] = $value;
        }
    }

    $new = [];
    foreach ($categories as $key => $value) {
        if (is_int($key)) {
            $new[$key] = $value;
        }
    }

    $new = convertToMultiDimensionalArray($new);

    $locale = 'az';

    foreach ($new as $key => $value) {

        if (! DB::table('category_channel')->where('r_category_id', $key)->exists()) {

            $id = DB::table('job_categories')->insertGetId([
                'image' => 'backend/image/default.png',
                'icon' => 'fas fa-pen',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('category_channel')->insert([
                'r_category_id' => $key,
                'l_category_id' => $id,
                'channel_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('job_category_translations')->insert([
                'job_category_id' => $id,
                'name' => $value,
                'locale' => $locale,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $id = DB::table('category_channel')->where('r_category_id', $key)->first();

            if (! DB::table('job_category_translations')
                ->where('job_category_id', $id->l_category_id)
                ->where('locale', $locale)->exists()) {

                DB::table('job_category_translations')->insert([
                    'job_category_id' => $id->l_category_id,
                    'name' => $value,
                    'locale' => $locale,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

        }

        echo $value.' - '.$key.'<br>';

    }

});
Route::get('/get-list-hellojob', function () {
    mb_internal_encoding('UTF-8');
    $response = Http::withHeaders([
        'authority' => 'www.hellojob.az',
        'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
        'cache-control' => 'max-age=0',
        'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'document',
        'sec-fetch-mode' => 'navigate',
        'sec-fetch-site' => 'none',
        'sec-fetch-user' => '?1',
        'upgrade-insecure-requests' => 1,
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
    ])
        ->get('https://www.hellojob.az/is-elanlari/texnologiya');

    if ($response->successful()) {
        $html = $response->body();
        $dom = new DOMDocument;
        libxml_use_internal_errors(true);
        $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        // $dom->loadHTML($html);
        libxml_clear_errors();
        $xpath = new DOMXPath($dom);
        $links = [];
        $divClass = 'col-md-8';
        $excludeDivClass = 'banner';
        $divElements = $xpath->query("//div[contains(@class, '$divClass')]");
        foreach ($divElements as $divElement) {
            $aElements = $xpath->query(".//a[not(ancestor::div[contains(@class, '$excludeDivClass')])]", $divElement);
            foreach ($aElements as $aElement) {
                $href = $aElement->getAttribute('href');
                $links[] = $href;
            }
        }
        $links = array_filter($links, function ($link) {
            return str_contains($link, '/vakansiya/');
        });
        $links = array_unique($links);
        $links = array_values($links);
        dd($links);

    } else {
        echo 'Failed to retrieve the webpage.';
    }
});
Route::get('/get-company-hellojob', function () {
    mb_internal_encoding('UTF-8');
    $response = Http::withHeaders([
        'authority' => 'www.hellojob.az',
        'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
        'cache-control' => 'max-age=0',
        'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'document',
        'sec-fetch-mode' => 'navigate',
        'sec-fetch-site' => 'none',
        'sec-fetch-user' => '?1',
        'upgrade-insecure-requests' => 1,
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
    ])
        ->get('https://www.hellojob.az/sirket/kapital-bank-8');

    $company_detail = [];

    if ($response->successful()) {
        $html = $response->body();
        $dom = new DOMDocument;
        $dom->encoding = 'UTF-8';
        libxml_use_internal_errors(true);
        $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        $xpath = new DOMXPath($dom);
        $divClass = 'company__head__top';
        $divElements = $xpath->query("//div[contains(@class, '$divClass')]");

        //Background Image
        $backgroundImageUrl = '';
        foreach ($divElements as $li) {
            $backgroundImage = $li->getAttribute('style');
            preg_match('/url\((.*?)\)/', $backgroundImage, $backgroundImageMatches);
            $backgroundImageUrl = $backgroundImageMatches[1] ?? '';
        }
        //Logo
        $imagesLazyLoading = '';
        $imgElements = $dom->getElementsByTagName('img');
        foreach ($imgElements as $imgElement) {
            $loadingAttributeValue = $imgElement->getAttribute('loading');
            if ($loadingAttributeValue === 'lazy') {
                $src = $imgElement->getAttribute('src');
                if (str_contains($src, 'https://www.hellojob.az/uploads/company/')) {
                    $imagesLazyLoading = $src;
                }
            }
        }

        $divClass_content = 'content';
        $divElements_content = $xpath->query("//div[contains(@class, '$divClass_content')]")->item(0);
        $company_content = $divElements_content->nodeValue;
        $company_detail = [
            'backgroundImageUrl' => $backgroundImageUrl,
            'imagesLazyLoading' => $imagesLazyLoading,
            'company_content' => $company_content,
        ];

        echo '<pre>';
        var_dump($company_detail);

    } else {
        echo 'Failed to retrieve the webpage.';
    }
});
Route::get('/get-job-hellojob', function () {
    mb_internal_encoding('UTF-8');
    $response = Http::withHeaders([
        'authority' => 'www.hellojob.az',
        'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language' => 'en-US,en;q=0.9,ru;q=0.8,tr;q=0.7',
        'cache-control' => 'max-age=0',
        'sec-ch-ua' => '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'document',
        'sec-fetch-mode' => 'navigate',
        'sec-fetch-site' => 'none',
        'sec-fetch-user' => '?1',
        'upgrade-insecure-requests' => 1,
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
    ])
        ->get('https://www.hellojob.az/vakansiya/evam-system-developer');

    $jobDetail = [];

    if ($response->successful()) {
        $html = $response->body();
        $dom = new DOMDocument;
        $dom->encoding = 'UTF-8';
        libxml_use_internal_errors(true);
        $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        libxml_clear_errors();
        $xpath = new DOMXPath($dom);

        // Find the <h3> element with class "resume__header__name"
        $nameElement = $xpath->query("//h3[@class='resume__header__name']")->item(0);
        $name = $nameElement->nodeValue;

        // Find the <a> element within the <p> element with class "resume__header__speciality"
        $aElement = $xpath->query("//p[@class='resume__header__speciality']/a")->item(0);
        $href = $aElement->getAttribute('href');
        $speciality = trim($aElement->nodeValue);

        $jobDetail = [
            'name' => $name,
            'speciality' => $speciality,
            'href' => $href,
        ];

        // \App\Models\Company::

        /*ToDo*/

        // Find the <h3> element with class "resume__title" containing job details
        $titleElement = $xpath->query("//h3[@class='resume__title']")->item(0);
        // Find all <div> elements with class "resume__item__text"
        $itemTextElements = $xpath->query("//div[@class='resume__item__text']");

        $jobDetails = [];
        foreach ($itemTextElements as $itemTextElement) {
            // Extract the job information
            $jobTitle = $itemTextElement->getElementsByTagName('h4')->item(0)->nodeValue;
            $jobCategory = $itemTextElement->getElementsByTagName('p')->item(0)->nodeValue;

            // Add the job information to the array
            $jobDetails[] = [
                'key' => $jobTitle,
                'value' => $jobCategory,
            ];
        }

        // Output the parsed values
        foreach ($jobDetails as $job) {

            $jobDetail['jobDetails'][Str::slug($job['value'])] = trim($job['key']);

        }
        //apply

        $applyLinkElement = $dom->getElementsByTagName('a');
        foreach ($applyLinkElement as $link) {
            $classes = $link->getAttribute('class');
            if (str_contains($classes, 'btn btn-apply')) {
                $applyText = $link->textContent;
                $href = $link->getAttribute('href');
                $nodeValue = $link->nodeValue;

                $jobDetail['jobDetails']['applyData'] = [
                    'applyText' => trim($applyText),
                    'href' => $href,
                    'nodeValue' => trim($nodeValue),
                ];
                break;
            }
        }

        // Find the <h3> element with class "resume__title" containing job details
        $titleElement = $xpath->query("//h3[@class='resume__title']")->item(1);

        // Find the <h3> element with class "resume__title"
        $h3Element = $xpath->query('//h3[@class="resume__title"]')->item(1);

        $h3Element?->parentNode->removeChild($h3Element);

        // Select the specific <div> with class "resume__block"
        $selectedDiv = $xpath->query('//div[@class="resume__block"]')->item(0);

        $nextDiv = $selectedDiv->nextSibling;
        while ($nextDiv && $nextDiv->nodeName !== 'div') {
            $nextDiv = $nextDiv->nextSibling;
        }

        $jobDetail['jobDetails']['job_info'] = trim($nextDiv->nodeValue);

        //job block
        $titleElement = $xpath->query('//div[@class="resume__block"]')->item(1);

        // Find the <h3> element with class "resume__title"
        $h3Element = $xpath->query('//h3[@class="resume__title"]')->item(1);

        $h3Element?->parentNode->removeChild($h3Element);

        $nextDiv = $titleElement->nextSibling;
        while ($nextDiv && $nextDiv->nodeName !== 'div') {
            $nextDiv = $nextDiv->nextSibling;
        }

        $nextDiv = str_ireplace('Vakansiyalardan daha tez xəbərdar olmaq üçün Telegram kanalımıza abunə olun!', '', $nextDiv->textContent);

        $jobDetail['jobDetails']['job_required'] = trim($nextDiv);

        echo '<pre>';
        var_dump($jobDetail);

    } else {
        echo 'Failed to retrieve the webpage.';
    }
});

Route::get('getJob', function () {
    set_time_limit(0);
    ini_set('max_execution_time', -1);
    ini_set('memory_limit', '4096M');

    $links = [
        '/is-elanlari/maliyye',
        '/is-elanlari/marketinq-4',
        '/is-elanlari/texnologiya',
        '/is-elanlari/satis-5',
        '/is-elanlari/xidmet',
        '/is-elanlari/dizayn',
        '/is-elanlari/muxtelif',
        '/is-elanlari/sehiyye',
        '/is-elanlari/tehsil-ve-elm',
        '/is-elanlari/huquq',
        '/is-elanlari/inzibati',
    ];

    $randomKeys = array_rand($links, 2);

    // Initialize an array to store the randomly selected links
    $randomLinks = [];

    // Populate the array with the randomly selected links
    foreach ($randomKeys as $key) {
        $randomLinks[] = $links[$key];
    }

    $successCount = 0;
    $errorCount = 0;

    try {
        foreach ($randomLinks as $link) {
            echo "Processing category: " . $link . "<br>";

            $getLastJobList = getJobListFromHelloJob('https://www.hellojob.az'.$link);

            if (empty($getLastJobList)) {
                echo "No jobs found for category: " . $link . "<br>";
                continue;
            }

            echo "Found " . count($getLastJobList) . " jobs in category: " . $link . "<br>";

            // Limit to first 2 jobs for testing company name fixes
            $getLastJobList = array_slice($getLastJobList, 0, 2);
            echo "Processing first 2 jobs for testing company name fixes...<br>";

            foreach ($getLastJobList as $key => $value) {
                try {
                    echo "Processing job: " . $value . "<br>";

                    $jobDetail = getJobDetailFromHelloJob($value);

                    if (empty($jobDetail)) {
                        echo "Failed to get job details for: " . $value . "<br>";
                        $errorCount++;
                        continue;
                    }

                    // Show basic info
                    echo "Company: " . ($jobDetail['speciality'] ?? 'No company');
                    if (!empty($jobDetail['logo_url'])) {
                        echo " | Logo: ✅";
                    }
                    echo "<br>";

                    $result = jobsImport($jobDetail);

                    if ($result) {
                        echo "Successfully imported job: " . ($jobDetail['name'] ?? 'Unknown') . "<br>";
                        $successCount++;
                    } else {
                        echo "Failed to import job: " . ($jobDetail['name'] ?? 'Unknown') . "<br>";
                        $errorCount++;
                    }

                    // Add a small delay to avoid overwhelming the server
                    usleep(500000); // 0.5 seconds

                } catch (\Exception $e) {
                    echo "Error processing job " . $value . ": " . $e->getMessage() . "<br>";
                    $errorCount++;
                    continue;
                }
            }
        }

        echo "<br><strong>Summary:</strong><br>";
        echo "Successfully imported: " . $successCount . " jobs<br>";
        echo "Errors encountered: " . $errorCount . " jobs<br>";
        echo "Categories processed: " . implode(', ', $randomLinks) . "<br>";

    } catch (\Exception $e) {
        echo "Fatal error: " . $e->getMessage() . "<br>";
        echo "Stack trace: " . $e->getTraceAsString() . "<br>";
    }

});
Route::get('/getCat', function () {

            $html = '<div class="swiper-wrapper" id="swiper-wrapper-403e380bb8734582" aria-live="polite" style="transform: translate3d(0px, 0px, 0px);">
        <div class="swiper-slide swiper-slide-active" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="1 / 15">
        <div class="new-cat-card apply-cv">
        <a href="/item/add">
        <img loading="lazy" src="https://www.hellojob.az/content/assets/images/vacancies/upload.svg" alt="Elan əlavə et">
        <div class="new-cat-name"><p>Elan əlavə et</p></div>
        <div class="new-cat-count"><p>Peşəkarlar sizi tapsın</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide swiper-slide-next" id="189" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="2 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/maliyye">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/maliyye-cat-6414340658330641434065833b-26NMIAq7bV3-yXq.svg" alt="Maliyyə">
        <div class="new-cat-name"><p>Maliyyə</p></div>
        <div class="new-cat-count"><p><span>102</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="192" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="3 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/marketinq-4">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/marketinq-cat-641433fe9e5c6641433fe9e5d3-rKuhtYV5f4UPa85.svg" alt="Marketinq">
        <div class="new-cat-name"><p>Marketinq</p></div>
        <div class="new-cat-count"><p><span>20</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="195" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="4 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/texnologiya">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/texnologiya-cat-641433be8b37d641433be8b38e-e9_0BJdsJbmUnVB.svg" alt="Texnologiya">
        <div class="new-cat-name"><p>Texnologiya</p></div>
        <div class="new-cat-count"><p><span>26</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="190" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="5 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/satis-5">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/satis-cat-64143417860756414341786081-vC1K1njTVifH3Sh.svg" alt="Satış">
        <div class="new-cat-name"><p>Satış</p></div>
        <div class="new-cat-count"><p><span>44</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="194" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="6 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/xidmet">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/xidmet-cat-6414342ff21226414342ff212e-m10Qat01rxfzfT3.svg" alt="Xidmət">
        <div class="new-cat-name"><p>Xidmət</p></div>
        <div class="new-cat-count"><p><span>53</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="193" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="7 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/dizayn">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/dizayn-cat-64143423043a064143423043ab-dW7E0i95SRjZ4mG.svg" alt="Dizayn">
        <div class="new-cat-name"><p>Dizayn</p></div>
        <div class="new-cat-count"><p><span>6</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="200" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="8 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/muxtelif">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/muxtelif-cat-6414343d74be26414343d74bed-Q30Ca59zmtb--84.svg" alt="Müxtəlif">
        <div class="new-cat-name"><p>Müxtəlif</p></div>
        <div class="new-cat-count"><p><span>3</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="197" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="9 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/sehiyye">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/tibb-ve-eczaciliq-cat-6414344d875b26414344d875bd-N7Sq_u6Y1JguQLo.svg" alt="Səhiyyə">
        <div class="new-cat-name"><p>Səhiyyə</p></div>
        <div class="new-cat-count"><p><span>4</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="199" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="10 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/tehsil-ve-elm">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/tehsil-ve-elm-cat-6414348e561bd6414348e561cb-lY0Gl-kw9Pyk_kP.svg" alt="Təhsil və elm">
        <div class="new-cat-name"><p>Təhsil və elm</p></div>
        <div class="new-cat-count"><p><span>21</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="196" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="11 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/huquq">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/18/huquqsunasliq-cat-64159435cecf664159435ced07-M7Vk7vB63FHKEg1.svg" alt="Hüquq">
        <div class="new-cat-name"><p>Hüquq</p></div>
        <div class="new-cat-count"><p><span>2</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" id="198" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="12 / 15">
        <div class="new-cat-card">
        <a href="/is-elanlari/inzibati">
        <img loading="lazy" src="https://www.hellojob.az/uploads/_categories/2023/03/17/inzibati-cat-641434674b8d6641434674b8e4-HuHF0Szj9u46Lut.svg" alt="İnzibati">
        <div class="new-cat-name"><p>İnzibati</p></div>
        <div class="new-cat-count"><p><span>21</span> iş elanı</p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="13 / 15">
        <div class="new-cat-card">
        <a href="/vakansiyalar/internships">
        <img loading="lazy" src="https://www.hellojob.az/content/assets/images/tecrube.svg" alt="Təcrübə">
        <div class="new-cat-name"><p>Təcrübə <br> proqramları</p></div>
        <div class="new-cat-count"><p></p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" style="width: 168.333px; margin-right: 32px;" role="group" aria-label="14 / 15">
        <div class="new-cat-card">
        <a href="/vakansiyalar/freelance">
        <img loading="lazy" src="https://www.hellojob.az/content/assets/images/freelance.svg" alt="Freelance">
        <div class="new-cat-name"><p>Freelance <br> işlər</p></div>
        <div class="new-cat-count"><p></p></div>
        </a>
        </div>
        </div>
        <div class="swiper-slide" role="group" aria-label="15 / 15" style="width: 168.333px; margin-right: 32px;">
        <div class="new-cat-card">
        <a href="/vakansiyalar/part-time">
        <img loading="lazy" src="https://www.hellojob.az/content/assets/images/part_time.svg" alt="Part-time">
        <div class="new-cat-name"><p>Part-time <br> işlər</p></div>
        <div class="new-cat-count"><p></p></div>
        </a>
        </div>
        </div>
        </div>';

    $dom = new DOMDocument;
    $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
    $xpath = new DOMXPath($dom);
    $divElements = $xpath->query("//div[contains(@class, 'swiper-slide')]");
    foreach ($divElements as $divElement) {
        $aElements = $xpath->query('.//a', $divElement);
        foreach ($aElements as $aElement) {
            $href = $aElement->getAttribute('href');
            $links[] = $href;
        }
    }
    $links = array_filter($links, function ($link) {
        return str_contains($link, '/is-elanlari/');
    });
    $links = array_unique($links);
    $links = array_values($links);
    dd($links);

});

Route::get('excell', function () {

    $csv = public_path('csvjson.json');

    $json = json_decode(file_get_contents($csv), true);

    //  echo '<pre>';
    //     print_r($json);
    // print_r($json[213]['key']);
    //    die;

    //$pattern = '/(\d+\.\d+(\.\d*)*)\.\s(.*?)(?=,|$)/';
    //    $pattern = '/^(\d+\.\d+\.\d+\.)\s(.+)/';
    //   $pattern = '/^(\d+\.\d+\.)\s(.+)/';
    //    $pattern = '/^([\d.-]+)\s(.*);$/';
    //    preg_match($pattern, $json[303]['key'], $matches);
    ////
    //    print_r($matches);
    //    die;

    //    $json = array_filter($json, function ($value) {
    //        return $value['key'] == '41.1-1. Cinayət təqibi üzrə icraata bəraətverici əsaslar olmadan xitam verilməli olduğu, lakin Azərbaycan Respublikası Cinayət Məcəlləsinin müddəalarına əsasən xüsusi müsadirənin tətbiq edilməsi üçün əsaslar müəyyən edildiyi hallarda, cinayət təqibi üzrə icraat bu Məcəllə ilə müəyyən edilmiş qaydada davam etdirilir və məhkəmənin yekun qərarının çıxarılması ilə başa çatdırılır.';
    //    });

    $data = [];
    foreach ($json as $key => $value) {

        $pattern = '/^(\d+\.\d+\.)\s(.+)/';
        $pattern0 = '/^(\d+\.\d+\.\d+\.)\s(.+)/';
        $pattern1 = '/(\d+\.\d+(\.\d*)*)\.\s(.*?);/';
        $pattern2 = '/Maddə (\d+(?:-\d+)?)\. (.*)/';
        $pattern3 = '/^(\d+(?:[-.]\d+)+)\s(.+)/';
        $pattern4 = '/^(\d+(?:[-.]?\d+)+)\s(.+)/';
        $pattern5 = '/^([\d.-]+)\s(.*);$/';
        $pattern6 = '/^(\d+\.\d+(?:-\d+)?\.)\s(.*?);$/';
        if (preg_match($pattern6, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $content = $matches[2];
            $data[$versionNumber] = $content;
        } elseif (preg_match($pattern5, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $content = $matches[2];
            $data[$versionNumber] = $content;
        } elseif (preg_match($pattern4, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $content = $matches[2];
            $data[$versionNumber] = $content;
        } elseif (preg_match($pattern3, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $content = $matches[2];
            $data[$versionNumber] = $content;
        } elseif (preg_match($pattern2, $value['key'], $matches)) {
            $number = $matches[1];
            $content = $matches[2];
            $data['Maddə '.$number.'.'] = $content;
        } elseif (preg_match($pattern, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $textAfterVersion = $matches[2];
            $data[$versionNumber] = $textAfterVersion;
        } elseif (preg_match($pattern0, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $textAfterVersion = $matches[2];
            $data[$versionNumber] = $textAfterVersion;
        } elseif (preg_match($pattern1, $value['key'], $matches)) {
            $versionNumber = $matches[1];
            $textAfterVersion = $matches[3];
            $data[$versionNumber] = $textAfterVersion;
        } else {

            $matches = explode(' ', $value['key']);

            $data[$matches[0]] = implode(' ', array_slice($matches, 1));
        }
    }

    //    echo '<pre>';
    //    print_r(array_diff_assoc( $json,$data));

    //die;
    //    echo '<pre>';
    //    echo count($data);
    //    echo '<br>';
    //    print_r($data);
    //    die;

    // Create a CSV file and write the data
    $csvFileName = public_path('cpm.csv');
    $csvFile = fopen($csvFileName, 'w');
    foreach ($data as $key => $text) {
        fputcsv($csvFile, [$key, $text]);
    }

    // Close the CSV file
    fclose($csvFile);

});

Route::get('token', function (\Illuminate\Http\Request $request) {
    $header = $request->header('x-A2Z-Auth');

    dd($header);
});
